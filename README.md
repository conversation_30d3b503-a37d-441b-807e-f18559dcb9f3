# 🚀 واتساب مراسل - النسخة المفتوحة المصدر

## 🎉 مرحباً بك في واتساب مراسل المجاني!

**تطبيق واتساب مراسل مفتوح المصدر بالكامل - مجاني للأبد!**

### 🌟 المميزات:
- 🆓 **مجاني 100%** - لا اشتراكات أو رسوم
- 🔓 **مفتوح المصدر** - كود كامل متاح للجميع
- 🌍 **للجميع** - بدون قيود جغرافية
- 🇸🇦 **واجهة عربية** - مصممة للمستخدمين العرب
- 📱 **سهل الاستخدام** - واجهة بسيطة وجميلة
- 📎 **إرسال الملفات** - دعم جميع أنواع الملفات
- 🔒 **خصوصية كاملة** - بياناتك تبقى عندك
- ⚡ **سريع وموثوق** - أداء ممتاز

## 🚀 التشغيل السريع:

### الطريقة الأولى (الأسهل):
```bash
cd whatsapp-messenger-opensource
```
**Windows**: اضغط مرتين على `start.bat`
**Mac/Linux**: شغل `./start.sh`

### الطريقة الثانية:
```bash
cd whatsapp-messenger-opensource
npm install
npm start
```

### ثم:
1. افتح `http://localhost:3001` في متصفحك
2. امسح QR Code بواتساب على هاتفك
3. ابدأ إرسال الرسائل والملفات!

## 📁 محتويات المجلد:

```
البرنامج/
├── README.md                           # هذا الملف
└── whatsapp-messenger-opensource/      # التطبيق الكامل
    ├── server.js                       # الخادم الرئيسي
    ├── package.json                    # معلومات المشروع
    ├── public/                         # الواجهة
    │   ├── index.html                  # الصفحة الرئيسية
    │   ├── style.css                   # التصميم
    │   └── script.js                   # التفاعل
    ├── README.md                       # دليل مفصل
    ├── INSTALL.md                      # دليل التثبيت
    ├── LICENSE                         # الترخيص
    ├── start.bat                       # تشغيل Windows
    ├── start.sh                        # تشغيل Mac/Linux
    └── COMPARISON.md                   # مقارنات
```

## 📖 التوثيق الكامل:

للحصول على دليل مفصل، اذهب إلى:
- **`whatsapp-messenger-opensource/README.md`** - الدليل الشامل
- **`whatsapp-messenger-opensource/INSTALL.md`** - دليل التثبيت
- **`whatsapp-messenger-opensource/COMPARISON.md`** - مقارنة مع النسخ الأخرى

### ملاحظات مهمة
- التطبيق يعمل على المنفذ 3000
- يتم حفظ بيانات الجلسة تلقائياً
- لإغلاق التطبيق، استخدم زر "تسجيل الخروج" في الواجهة

### استكشاف الأخطاء
- إذا لم يفتح المتصفح تلقائياً، افتحه يدوياً واذهب إلى: `http://localhost:3000`
- تأكد من عدم استخدام المنفذ 3000 من قبل تطبيق آخر
- في حالة وجود مشاكل، أعد تشغيل التطبيق

## 🤝 المساهمة والدعم:

### للمطورين:
- 🐛 **أبلغ عن الأخطاء**: افتح Issue على GitHub
- 💡 **اقترح ميزات**: شارك أفكارك الجديدة
- 🔧 **ساهم بالكود**: أرسل Pull Request
- 📖 **حسن التوثيق**: ساعد في تحسين الأدلة

### للمستخدمين:
- ⭐ **قيم المشروع**: أعط نجمة على GitHub
- 📢 **انشر الكلمة**: أخبر أصدقاءك عن التطبيق
- 🧪 **اختبر الميزات**: جرب الميزات الجديدة وأبلغ عن المشاكل
- 💬 **انضم للمجتمع**: شارك في النقاشات والتطوير

## ⚖️ الترخيص:

هذا البرنامج مرخص تحت **رخصة MIT** - مجاني للجميع:
- ✅ استخدام شخصي وتجاري
- ✅ تعديل وتطوير الكود
- ✅ توزيع ومشاركة التطبيق
- ✅ إنشاء نسخ محسنة وبيعها

---

**🎊 استمتع بالحرية والإبداع مع واتساب مراسل المفتوح المصدر! 🚀**