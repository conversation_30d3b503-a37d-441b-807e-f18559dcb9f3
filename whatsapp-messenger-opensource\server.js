const express = require('express');
const { Client, LocalAuth, MessageMedia } = require('whatsapp-web.js');
const qrcode = require('qrcode');
const http = require('http');
const socketIo = require('socket.io');
const path = require('path');
const cors = require('cors');
const helmet = require('helmet');
const compression = require('compression');
const multer = require('multer');
const fs = require('fs');

// إعداد التطبيق
const app = express();
const server = http.createServer(app);
const io = socketIo(server, {
    cors: {
        origin: "*",
        methods: ["GET", "POST"]
    }
});

const PORT = process.env.PORT || 3001;

// إعدادات الأمان والضغط
app.use(helmet({
    contentSecurityPolicy: false
}));
app.use(compression());
app.use(cors());
app.use(express.json());
app.use(express.urlencoded({ extended: true }));
app.use(express.static(path.join(__dirname, 'public')));

// إعداد multer لرفع الملفات
const storage = multer.diskStorage({
    destination: function (req, file, cb) {
        const uploadDir = './uploads';
        if (!fs.existsSync(uploadDir)) {
            fs.mkdirSync(uploadDir, { recursive: true });
        }
        cb(null, uploadDir);
    },
    filename: function (req, file, cb) {
        const uniqueSuffix = Date.now() + '-' + Math.round(Math.random() * 1E9);
        cb(null, file.fieldname + '-' + uniqueSuffix + path.extname(file.originalname));
    }
});

const upload = multer({
    storage: storage,
    limits: {
        fileSize: 10 * 1024 * 1024 // 10MB
    },
    fileFilter: function (req, file, cb) {
        // السماح بجميع أنواع الملفات
        cb(null, true);
    }
});

// متغيرات عامة
let client;
let qrCodeData = null;
let isClientReady = false;
let connectedUsers = new Set();
let isInitializing = false; // منع التهيئة المتعددة

// إنشاء عميل WhatsApp
async function initializeWhatsAppClient() {
    // منع التهيئة المتعددة
    if (isInitializing) {
        console.log('⚠️ التهيئة قيد التقدم، تم تجاهل الطلب');
        return;
    }

    isInitializing = true;
    console.log('🚀 بدء تشغيل واتساب مراسل - النسخة المفتوحة المصدر');

    try {
        // التأكد من إنهاء العميل السابق
        if (client) {
            try {
                await client.destroy();
                console.log('🗑️ تم إنهاء العميل السابق');
            } catch (e) {
                console.log('⚠️ تم تجاهل خطأ في إنهاء العميل السابق:', e.message);
            }
            client = null;
        }

        // إعادة تعيين المتغيرات
        isClientReady = false;
        qrCodeData = null;

    client = new Client({
        authStrategy: new LocalAuth({
            dataPath: './session'
        }),
        puppeteer: {
            headless: true,
            args: [
                '--no-sandbox',
                '--disable-setuid-sandbox',
                '--disable-dev-shm-usage',
                '--disable-accelerated-2d-canvas',
                '--no-first-run',
                '--no-zygote',
                '--single-process',
                '--disable-gpu'
            ]
        }
    });

    // عند استلام QR Code
    client.on('qr', (qr) => {
        console.log('📱 تم إنشاء QR Code جديد');
        qrcode.toDataURL(qr, (err, url) => {
            if (err) {
                console.error('❌ خطأ في إنشاء QR Code:', err);
                return;
            }
            qrCodeData = url;
            io.emit('qr', url);
        });
    });

    // عند جاهزية العميل
    client.on('ready', () => {
        console.log('✅ باتريوت جاهز للاستخدام!');
        isClientReady = true;
        qrCodeData = null;
        isInitializing = false; // إعادة تعيين متغير التهيئة عند الجاهزية

        // إرسال حدث الجاهزية لجميع المستخدمين المتصلين
        console.log(`📡 إرسال حدث الجاهزية لـ ${connectedUsers.size} مستخدم`);
        io.emit('ready', { message: 'Patriot WhatsApp client is ready!' });
    });

    // عند استلام رسالة
    client.on('message', async (message) => {
        console.log(`📨 رسالة جديدة من ${message.from}: ${message.body}`);
        io.emit('message', {
            from: message.from,
            body: message.body,
            timestamp: message.timestamp,
            type: message.type
        });
    });

    // عند قطع الاتصال
    client.on('disconnected', (reason) => {
        console.log('❌ تم قطع الاتصال:', reason);
        isClientReady = false;
        qrCodeData = null;
        isInitializing = false; // إعادة تعيين متغير التهيئة عند قطع الاتصال
        io.emit('disconnected', { reason });
    });

    // عند حدوث خطأ
    client.on('auth_failure', (msg) => {
        console.error('❌ فشل في المصادقة:', msg);
        isClientReady = false;
        qrCodeData = null;
        isInitializing = false; // إعادة تعيين متغير التهيئة عند فشل المصادقة
        io.emit('auth_failure', { message: msg });
    });

    // معالجة الأخطاء العامة
    client.on('error', (error) => {
        console.error('❌ خطأ في العميل:', error);
        isInitializing = false; // إعادة تعيين متغير التهيئة عند حدوث خطأ
    });

        // بدء العميل
        await client.initialize();
        console.log('✅ تم تهيئة العميل بنجاح');

    } catch (error) {
        console.error('❌ فشل في تهيئة العميل:', error);
        client = null;
        isClientReady = false;
        qrCodeData = null;
    } finally {
        isInitializing = false; // إعادة تعيين متغير التهيئة
    }
}

// المسارات (Routes)

// الصفحة الرئيسية
app.get('/', (req, res) => {
    res.sendFile(path.join(__dirname, 'public', 'index.html'));
});

// الحصول على حالة الاتصال
app.get('/api/status', (req, res) => {
    res.json({
        isReady: isClientReady,
        hasQR: qrCodeData !== null,
        connectedUsers: connectedUsers.size,
        isInitializing: isInitializing
    });
});

// الحصول على QR Code
app.get('/api/qr', (req, res) => {
    if (qrCodeData) {
        res.json({
            success: true,
            qr: qrCodeData
        });
    } else {
        res.json({
            success: false,
            message: 'لا يوجد QR Code متاح'
        });
    }
});

// إعادة تعيين حالة التهيئة (للطوارئ)
app.post('/api/reset-init', (req, res) => {
    console.log('🔄 إعادة تعيين حالة التهيئة...');
    isInitializing = false;
    res.json({
        success: true,
        message: 'تم إعادة تعيين حالة التهيئة'
    });
});

// إرسال رسالة
app.post('/api/send-message', async (req, res) => {
    try {
        if (!isClientReady) {
            return res.status(400).json({ 
                success: false, 
                message: 'WhatsApp client is not ready' 
            });
        }

        const { number, message } = req.body;
        
        if (!number || !message) {
            return res.status(400).json({ 
                success: false, 
                message: 'Number and message are required' 
            });
        }

        // تنسيق الرقم
        const chatId = number.includes('@c.us') ? number : `${number}@c.us`;
        
        // إرسال الرسالة
        const result = await client.sendMessage(chatId, message);
        
        console.log(`✅ تم إرسال رسالة إلى ${number}: ${message}`);
        
        res.json({ 
            success: true, 
            message: 'Message sent successfully',
            messageId: result.id._serialized
        });
        
    } catch (error) {
        console.error('❌ خطأ في إرسال الرسالة:', error);
        res.status(500).json({ 
            success: false, 
            message: 'Failed to send message',
            error: error.message 
        });
    }
});

// إرسال ملف
app.post('/api/send-file', upload.single('file'), async (req, res) => {
    try {
        if (!isClientReady) {
            return res.status(400).json({
                success: false,
                message: 'WhatsApp client is not ready'
            });
        }

        const { number, caption } = req.body;
        const file = req.file;

        if (!number || !file) {
            return res.status(400).json({
                success: false,
                message: 'Number and file are required'
            });
        }

        // تنسيق الرقم
        const chatId = number.includes('@c.us') ? number : `${number}@c.us`;

        // إرسال الملف
        const media = MessageMedia.fromFilePath(file.path);
        const result = await client.sendMessage(chatId, media, { caption: caption || '' });

        console.log(`✅ تم إرسال ملف إلى ${number}: ${file.originalname}`);

        // حذف الملف بعد الإرسال
        fs.unlinkSync(file.path);

        res.json({
            success: true,
            message: 'File sent successfully',
            messageId: result.id._serialized
        });

    } catch (error) {
        console.error('❌ خطأ في إرسال الملف:', error);

        // حذف الملف في حالة الخطأ
        if (req.file && fs.existsSync(req.file.path)) {
            fs.unlinkSync(req.file.path);
        }

        res.status(500).json({
            success: false,
            message: 'Failed to send file',
            error: error.message
        });
    }
});

// بدء تهيئة العميل
app.post('/api/initialize', async (req, res) => {
    try {
        console.log('🚀 طلب تهيئة العميل...');

        // التحقق من التهيئة الجارية
        if (isInitializing) {
            return res.json({
                success: false,
                message: 'التهيئة قيد التقدم، يرجى الانتظار'
            });
        }

        // إذا كان العميل موجود ومتصل، قم بتسجيل الخروج أولاً لإظهار QR
        if (client && isClientReady) {
            console.log('🔄 العميل متصل، سيتم تسجيل الخروج لإظهار QR جديد');
            try {
                await client.logout();
                await client.destroy();
            } catch (error) {
                console.log('⚠️ خطأ في تسجيل الخروج:', error.message);
            }
            client = null;
            isClientReady = false;
            qrCodeData = null;
        }

        // إنشاء عميل جديد
        console.log('🔄 إنشاء عميل واتساب جديد...');
        await initializeWhatsAppClient();

        res.json({
            success: true,
            message: 'تم بدء عملية التهيئة، انتظر QR Code'
        });
    } catch (error) {
        console.error('❌ خطأ في التهيئة:', error);
        res.status(500).json({
            success: false,
            message: 'فشل في التهيئة'
        });
    }
});

// إعادة تشغيل العميل
app.post('/api/restart', async (req, res) => {
    try {
        if (client) {
            await client.destroy();
        }
        
        setTimeout(async () => {
            await initializeWhatsAppClient();
        }, 2000);
        
        res.json({ 
            success: true, 
            message: 'Client restarting...' 
        });
        
    } catch (error) {
        console.error('❌ خطأ في إعادة التشغيل:', error);
        res.status(500).json({ 
            success: false, 
            message: 'Failed to restart client',
            error: error.message 
        });
    }
});

// تسجيل الخروج
app.post('/api/logout', async (req, res) => {
    try {
        console.log('🚪 طلب تسجيل خروج من:', req.ip);

        // إعادة تعيين المتغيرات فوراً
        isClientReady = false;
        qrCodeData = null;
        isInitializing = false;

        if (client) {
            try {
                console.log('🔄 تسجيل الخروج من واتساب...');
                // تسجيل الخروج من واتساب أولاً (هذا يقطع الاتصال من الجوال)
                await client.logout();
                console.log('✅ تم تسجيل الخروج من واتساب');

                // ثم إنهاء العميل
                await client.destroy();
                console.log('✅ تم إنهاء عميل WhatsApp');

            } catch (logoutError) {
                console.log('⚠️ خطأ في تسجيل الخروج:', logoutError.message);
                // حتى لو فشل logout، نحاول destroy
                try {
                    await client.destroy();
                    console.log('✅ تم إنهاء العميل بعد فشل logout');
                } catch (destroyError) {
                    console.log('⚠️ خطأ في إنهاء العميل:', destroyError.message);
                }
            }

            client = null;
        }

        // حذف ملفات الجلسة لضمان عدم الاتصال التلقائي
        try {
            const fs = require('fs').promises;
            const path = require('path');

            const sessionPaths = [
                path.join(__dirname, 'session'),
                path.join(__dirname, '.wwebjs_auth'),
                path.join(__dirname, '.wwebjs_cache')
            ];

            for (const sessionPath of sessionPaths) {
                try {
                    await fs.rm(sessionPath, { recursive: true, force: true });
                    console.log(`🗑️ تم حذف: ${sessionPath}`);
                } catch (rmError) {
                    // تجاهل الأخطاء إذا كان المجلد غير موجود
                    if (rmError.code !== 'ENOENT') {
                        console.log(`⚠️ خطأ في حذف ${sessionPath}:`, rmError.message);
                    }
                }
            }
        } catch (cleanupError) {
            console.log('⚠️ خطأ في تنظيف الملفات:', cleanupError.message);
        }

        // إشعار جميع المستخدمين بتسجيل الخروج
        io.emit('disconnected', { reason: 'LOGOUT' });
        io.emit('status', {
            isReady: false,
            hasQR: false
        });

        // إرسال استجابة
        res.json({
            success: true,
            message: 'تم تسجيل الخروج بنجاح'
        });

    } catch (error) {
        console.error('❌ خطأ عام في تسجيل الخروج:', error);
        res.json({
            success: true,
            message: 'تم تسجيل الخروج'
        });
    }
});

// Socket.IO للاتصال المباشر
io.on('connection', (socket) => {
    console.log('🔗 مستخدم جديد متصل:', socket.id);
    connectedUsers.add(socket.id);
    
    // إرسال الحالة الحالية للمستخدم الجديد
    socket.emit('status', {
        isReady: isClientReady,
        hasQR: qrCodeData !== null
    });
    
    if (qrCodeData) {
        socket.emit('qr', qrCodeData);
    }
    
    // تسجيل الخروج
    socket.on('logout', async () => {
        try {
            console.log('🚪 طلب تسجيل خروج من:', socket.id);
            if (client) {
                // تسجيل الخروج من واتساب أولاً
                await client.logout();
                await client.destroy();
                client = null;
                isClientReady = false;
                qrCodeData = null;
                isInitializing = false;
                console.log('✅ تم تسجيل الخروج بنجاح');

                // حذف ملفات الجلسة
                try {
                    const fs = require('fs').promises;
                    const path = require('path');

                    const sessionPaths = [
                        path.join(__dirname, 'session'),
                        path.join(__dirname, '.wwebjs_auth'),
                        path.join(__dirname, '.wwebjs_cache')
                    ];

                    for (const sessionPath of sessionPaths) {
                        try {
                            await fs.rm(sessionPath, { recursive: true, force: true });
                            console.log(`🗑️ تم حذف: ${sessionPath}`);
                        } catch (rmError) {
                            if (rmError.code !== 'ENOENT') {
                                console.log(`⚠️ خطأ في حذف ${sessionPath}:`, rmError.message);
                            }
                        }
                    }
                } catch (cleanupError) {
                    console.log('⚠️ خطأ في تنظيف الملفات:', cleanupError.message);
                }

                // إشعار جميع المستخدمين
                io.emit('disconnected', { reason: 'LOGOUT' });
                io.emit('status', {
                    isReady: false,
                    hasQR: false
                });

                socket.emit('logoutSuccess');
            }
        } catch (error) {
            console.error('❌ خطأ في تسجيل الخروج:', error);
            socket.emit('logoutError', error.message);
        }
    });

    socket.on('disconnect', () => {
        console.log('❌ مستخدم منقطع:', socket.id);
        connectedUsers.delete(socket.id);
    });
});

// بدء الخادم
server.listen(PORT, async () => {
    console.log(`🌐 الخادم يعمل على المنفذ ${PORT}`);
    console.log(`🔗 يمكنك الوصول للتطبيق عبر: http://localhost:${PORT}`);
    console.log('🚀 باتريوت - مراسل واتساب المتقدم والسريع!');

    // بدء عميل WhatsApp
    await initializeWhatsAppClient();
});

// معالجة إغلاق التطبيق
process.on('SIGINT', async () => {
    console.log('🛑 إيقاف التطبيق...');
    if (client) {
        await client.destroy();
    }
    process.exit(0);
});

process.on('SIGTERM', async () => {
    console.log('🛑 إنهاء التطبيق...');
    if (client) {
        await client.destroy();
    }
    process.exit(0);
});
