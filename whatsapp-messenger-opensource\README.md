# واتساب مراسل - النسخة المفتوحة المصدر 🚀

## نسخة مجانية ومفتوحة المصدر بالكامل من واتساب مراسل

[![License: MIT](https://img.shields.io/badge/License-MIT-yellow.svg)](https://opensource.org/licenses/MIT)
[![Node.js](https://img.shields.io/badge/Node.js-16+-green.svg)](https://nodejs.org/)
[![Arabic](https://img.shields.io/badge/Language-Arabic-blue.svg)](README.md)

## 🎉 المميزات

- 🆓 **مجاني بالكامل** - لا يتطلب اشتراك أو دفع أي رسوم
- 🔓 **مفتوح المصدر** - يمكن تعديله وتطويره بحرية
- 🌍 **للجميع** - متاح لأي شخص في العالم بدون قيود
- 🇸🇦 **واجهة عربية** - مصمم خصيصاً للمستخدمين العرب
- 📱 **سهل الاستخدام** - واجهة بسيطة وجميلة
- 🔒 **آمن وموثوق** - لا يحفظ بياناتك على خوادم خارجية
- ⚡ **سريع وخفيف** - أداء ممتاز بدون بطء
- 📎 **إرسال الملفات** - دعم إرسال جميع أنواع الملفات

## 🛠️ المتطلبات

- Node.js 16 أو أحدث
- npm أو yarn
- متصفح ويب حديث

## 📦 التثبيت والتشغيل

### 1. تحميل المشروع
```bash
git clone https://github.com/your-username/whatsapp-messenger-opensource.git
cd whatsapp-messenger-opensource
```

### 2. تثبيت المتطلبات
```bash
npm install
```

### 3. تشغيل التطبيق

#### الطريقة الأولى (الأسهل):
**Windows**:
- اضغط مرتين على `start.bat` (مفصل مع تشخيص)
- أو اضغط مرتين على `quick-start.bat` (سريع)

**Mac/Linux**:
- شغل `./start.sh`

#### الطريقة الثانية (يدوي):
```bash
npm start
```

#### إذا واجهت مشاكل:
1. **تشخيص المشكلة**: اضغط مرتين على `diagnose.bat`
2. **اقرأ دليل الأخطاء**: `TROUBLESHOOTING.md`
3. **جرب التشغيل السريع**: `quick-start.bat`

### 4. فتح التطبيق
افتح متصفحك واذهب إلى: `http://localhost:3001`

## 🚀 طريقة الاستخدام

### الخطوة 1: ربط الهاتف
1. شغل التطبيق
2. امسح QR Code باستخدام واتساب على هاتفك
3. اذهب إلى واتساب > الإعدادات > الأجهزة المرتبطة > ربط جهاز

### الخطوة 2: إرسال الرسائل
1. أدخل رقم الهاتف مع رمز البلد (مثال: 966501234567)
2. اكتب رسالتك
3. اضغط "إرسال الرسالة"

### الخطوة 3: إرسال الملفات
1. أدخل رقم الهاتف
2. اختر الملف أو اسحبه إلى المنطقة المخصصة
3. أضف تعليق (اختياري)
4. اضغط "إرسال الملف"

## 📁 هيكل المشروع

```
whatsapp-messenger-opensource/
├── server.js              # الخادم الرئيسي
├── package.json           # معلومات المشروع والمتطلبات
├── public/                # الملفات العامة
│   ├── index.html         # الواجهة الرئيسية
│   ├── style.css          # ملف التصميم
│   └── script.js          # ملف JavaScript
├── session/               # بيانات الجلسة (يتم إنشاؤها تلقائياً)
├── uploads/               # الملفات المرفوعة (مؤقت)
└── README.md              # هذا الملف
```

## 🔧 التخصيص والتطوير

### تغيير المنفذ
```bash
PORT=8080 npm start
```

### إضافة ميزات جديدة
1. عدل `server.js` لإضافة APIs جديدة
2. عدل `public/script.js` لإضافة وظائف جديدة
3. عدل `public/style.css` لتحسين التصميم

### البناء للإنتاج
```bash
npm run build
```

## 🤝 المساهمة

نرحب بجميع المساهمات! يمكنك:

1. **إبلاغ عن الأخطاء** - افتح issue جديد
2. **اقتراح تحسينات** - شارك أفكارك
3. **كتابة الكود** - أرسل pull request
4. **الترجمة** - ساعد في ترجمة التطبيق
5. **التوثيق** - حسن من الوثائق

### خطوات المساهمة
1. Fork المشروع
2. أنشئ branch جديد (`git checkout -b feature/amazing-feature`)
3. Commit التغييرات (`git commit -m 'Add amazing feature'`)
4. Push إلى Branch (`git push origin feature/amazing-feature`)
5. افتح Pull Request

## 📄 الترخيص

هذا المشروع مرخص تحت رخصة MIT - انظر ملف [LICENSE](LICENSE) للتفاصيل.

### ما يعنيه هذا:
- ✅ استخدام مجاني للأغراض الشخصية والتجارية
- ✅ تعديل وتطوير الكود
- ✅ توزيع ومشاركة التطبيق
- ✅ بيع نسخ محسنة
- ❌ لا ضمانات من المطور

## 🆘 الدعم والمساعدة

### الأسئلة الشائعة

**س: هل التطبيق آمن؟**
ج: نعم، التطبيق لا يحفظ بياناتك على خوادم خارجية ويستخدم نفس تقنية واتساب ويب.

**س: هل يمكنني استخدامه تجارياً؟**
ج: نعم، الترخيص يسمح بالاستخدام التجاري.

**س: كيف أحدث التطبيق؟**
ج: احذف المجلد القديم وحمل النسخة الجديدة، أو استخدم `git pull`.

**س: ملف start.bat يختفي بسرعة؟**
ج: اضغط مرتين على `diagnose.bat` لتشخيص المشكلة، أو اقرأ `TROUBLESHOOTING.md`.

**س: خطأ "Node.js غير مثبت"؟**
ج: حمل وثبت Node.js من https://nodejs.org/ واختر النسخة LTS.

**س: خطأ "المنفذ مستخدم"؟**
ج: أغلق التطبيق الآخر أو غير المنفذ في `server.js`.

### استكشاف الأخطاء
- **تشخيص سريع**: شغل `diagnose.bat`
- **دليل الأخطاء**: اقرأ `TROUBLESHOOTING.md`
- **تشغيل سريع**: جرب `quick-start.bat`

### طلب المساعدة
- افتح [Issue جديد](https://github.com/your-username/whatsapp-messenger-opensource/issues)
- راسلنا على [البريد الإلكتروني](mailto:<EMAIL>)
- انضم إلى [مجتمع Discord](https://discord.gg/example)

## 🙏 شكر وتقدير

- [whatsapp-web.js](https://github.com/pedroslopez/whatsapp-web.js) - المكتبة الأساسية
- [Express.js](https://expressjs.com/) - إطار العمل
- [Socket.IO](https://socket.io/) - الاتصال المباشر
- جميع المساهمين في المشروع

## 🔮 الخطط المستقبلية

- [ ] دعم الرسائل الجماعية
- [ ] جدولة الرسائل
- [ ] إحصائيات متقدمة
- [ ] دعم البوتات
- [ ] واجهة إدارة متقدمة
- [ ] دعم قواعد البيانات
- [ ] API للمطورين

---

**صنع بـ ❤️ للمجتمع العربي**

إذا أعجبك المشروع، لا تنس إعطاؤه ⭐ على GitHub!
