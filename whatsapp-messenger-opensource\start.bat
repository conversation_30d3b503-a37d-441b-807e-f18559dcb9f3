@echo off
title Patriot - Advanced WhatsApp Messenger

echo.
echo ========================================
echo        Patriot - WhatsApp Messenger
echo ========================================
echo.
echo Starting application...
echo.

REM Check for Node.js
echo Checking Node.js...
node --version >nul 2>&1
if %errorlevel% neq 0 (
    echo.
    echo Error: Node.js is not installed
    echo Please install Node.js from: https://nodejs.org/
    echo.
    pause
    exit /b 1
)

REM Display versions
for /f "tokens=*" %%i in ('node --version') do set NODE_VERSION=%%i
for /f "tokens=*" %%i in ('npm --version') do set NPM_VERSION=%%i
echo Node.js: %NODE_VERSION%
echo npm: %NPM_VERSION%
echo.

REM Install dependencies if needed
if not exist "node_modules" (
    echo Installing dependencies...
    npm install
    if %errorlevel% neq 0 (
        echo Failed to install dependencies
        pause
        exit /b 1
    )
)

REM Start application
echo Starting server...
echo Open: http://localhost:3001
echo.
echo Press Ctrl+C to stop
echo.

REM Open browser after 2 seconds
start "" cmd /c "timeout /t 2 >nul && start http://localhost:3001"

REM Start the server
npm start

pause
chcp 65001 >nul
title WhatsApp Messenger - Open Source Version

echo.
echo ========================================
echo   WhatsApp Messenger - Open Source
echo ========================================
echo.
echo 🚀 Starting application...
echo.

REM Check for Node.js
echo 🔍 Checking Node.js...
node --version >nul 2>&1
if %errorlevel% neq 0 (
    echo.
    echo ❌ Error: Node.js is not installed on the system
    echo.
    echo 📥 Please install Node.js from: https://nodejs.org/
    echo 💡 Choose the LTS version (recommended)
    echo.
    echo 📋 Installation steps:
    echo    1. Go to https://nodejs.org/
    echo    2. Download the LTS version
    echo    3. Run the downloaded file
    echo    4. Follow the instructions
    echo    5. Restart this file
    echo.
    pause
    exit /b 1
)

REM Display Node.js version
for /f "tokens=*" %%i in ('node --version') do set NODE_VERSION=%%i
for /f "tokens=*" %%i in ('npm --version') do set NPM_VERSION=%%i
echo ✅ Node.js: %NODE_VERSION%
echo ✅ npm: %NPM_VERSION%
echo.

REM Check for node_modules
if not exist "node_modules" (
    echo 📦 Installing dependencies for the first time...
    echo ⏳ This may take a few minutes...
    echo.
    npm install
    if %errorlevel% neq 0 (
        echo.
        echo ❌ Failed to install dependencies
        echo.
        echo 🔧 Suggested solutions:
        echo    1. Check internet connection
        echo    2. Run Command Prompt as Administrator
        echo    3. Try command: npm install --force
        echo.
        pause
        exit /b 1
    )
    echo.
    echo ✅ Dependencies installed successfully!
    echo.
) else (
    echo ✅ Dependencies already installed
    echo.
)

REM Start the application
echo 🌐 Starting server...
echo 🔗 Application will open at: http://localhost:3001
echo.
echo ⚠️  To stop the application, press Ctrl+C in this window
echo 💡 Do not close this window while using the application
echo.

REM Open browser automatically after 3 seconds
start "" cmd /c "timeout /t 3 >nul && start http://localhost:3001"

REM Start the application
npm start

REM In case of application shutdown
echo.
echo 🛑 Application stopped
echo.
pause
