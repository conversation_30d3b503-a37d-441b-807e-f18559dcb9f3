@echo off
title واتساب مراسل - تشغيل سريع

echo 🚀 واتساب مراسل - تشغيل سريع
echo.

REM التحقق من Node.js
node --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ Node.js غير مثبت
    echo 📥 حمل من: https://nodejs.org/
    pause
    exit
)

REM تثبيت المتطلبات إذا لم تكن موجودة
if not exist "node_modules" (
    echo 📦 تثبيت المتطلبات...
    npm install
)

REM تشغيل التطبيق
echo ✅ بدء التشغيل...
echo 🌐 افتح: http://localhost:3001
echo.

REM فتح المتصفح
timeout /t 2 >nul
start http://localhost:3001

REM تشغيل الخادم
node server.js

pause
