#!/bin/bash

# واتساب مراسل - النسخة المفتوحة المصدر
# WhatsApp Messenger - Open Source Version

echo ""
echo "========================================"
echo "  واتساب مراسل - النسخة المفتوحة المصدر"
echo "  WhatsApp Messenger - Open Source"
echo "========================================"
echo ""
echo "🚀 بدء تشغيل التطبيق..."
echo ""

# التحقق من وجود Node.js
if ! command -v node &> /dev/null; then
    echo "❌ خطأ: Node.js غير مثبت على النظام"
    echo ""
    echo "يرجى تثبيت Node.js من: https://nodejs.org/"
    echo ""
    echo "أو استخدم الأوامر التالية:"
    echo "Ubuntu/Debian: sudo apt install nodejs npm"
    echo "macOS: brew install node"
    echo "CentOS/RHEL: sudo yum install nodejs npm"
    echo ""
    exit 1
fi

# عرض إصدار Node.js
NODE_VERSION=$(node --version)
NPM_VERSION=$(npm --version)
echo "✅ Node.js: $NODE_VERSION"
echo "✅ npm: $NPM_VERSION"
echo ""

# التحقق من وجود node_modules
if [ ! -d "node_modules" ]; then
    echo "📦 تثبيت المتطلبات..."
    npm install
    if [ $? -ne 0 ]; then
        echo "❌ فشل في تثبيت المتطلبات"
        exit 1
    fi
fi

echo "✅ تم تثبيت المتطلبات بنجاح"
echo ""
echo "🌐 تشغيل الخادم..."
echo "🔗 سيتم فتح التطبيق على: http://localhost:3001"
echo ""
echo "⚠️  لإيقاف التطبيق، اضغط Ctrl+C"
echo ""

# تشغيل التطبيق
npm start
