<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>باتريوت - مراسل واتساب</title>
    <link rel="stylesheet" href="style.css">
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
</head>
<body>
    <!-- Header -->
    <header class="header">
        <div class="container">
            <div class="header-content">
                <div class="logo">
                    <i class="fas fa-fighter-jet"></i>
                    <h1>باتريوت</h1>
                    <span class="version">مراسل واتساب المتقدم</span>
                </div>
                <div class="header-actions">
                    <button class="btn-header btn-logout" id="headerLogoutBtn">
                        <i class="fas fa-sign-out-alt"></i>
                        تسجيل الخروج
                    </button>
                    <div class="status-indicator">
                        <div class="status-dot" id="statusDot"></div>
                        <span id="statusText">جاري التحميل...</span>
                    </div>
                </div>
            </div>
        </div>
    </header>

    <!-- Main Content -->
    <main class="main">
        <div class="container">
            <!-- Welcome Section -->
            <section class="welcome-section" id="welcomeSection">
                <div class="welcome-card">
                    <div class="welcome-icon">
                        <i class="fas fa-fighter-jet"></i>
                    </div>
                    <h2>مرحباً بك في باتريوت</h2>
                    <p>مراسل واتساب المتقدم والسريع</p>
                    <div class="features">
                        <div class="feature">
                            <i class="fas fa-fighter-jet"></i>
                            <span>سريع كالبرق</span>
                        </div>
                        <div class="feature">
                            <i class="fas fa-shield-alt"></i>
                            <span>آمن وموثوق</span>
                        </div>
                        <div class="feature">
                            <i class="fas fa-layer-group"></i>
                            <span>واجهة متقدمة</span>
                        </div>
                        <div class="feature">
                            <i class="fas fa-language"></i>
                            <span>واجهة عربية</span>
                        </div>
                    </div>
                    <div class="welcome-actions">
                        <button class="btn-start" id="startBtn">
                            <i class="fas fa-rocket"></i>
                            ابدأ الآن
                        </button>
                    </div>
                </div>
            </section>

            <!-- QR Code Section -->
            <section class="qr-section" id="qrSection" style="display: none;">
                <div class="qr-card">
                    <div class="qr-header">
                        <h3>امسح الكود للاتصال</h3>
                        <p>استخدم تطبيق واتساب على هاتفك لمسح هذا الكود</p>
                    </div>
                    <div class="qr-container">
                        <div class="qr-placeholder" id="qrPlaceholder">
                            <i class="fas fa-qrcode"></i>
                            <span>جاري إنشاء الكود...</span>
                        </div>
                        <img id="qrImage" style="display: none;" alt="QR Code">
                    </div>
                    <div class="qr-instructions">
                        <div class="instruction">
                            <span class="step">1</span>
                            <span>افتح واتساب على هاتفك</span>
                        </div>
                        <div class="instruction">
                            <span class="step">2</span>
                            <span>اذهب إلى الإعدادات > الأجهزة المرتبطة</span>
                        </div>
                        <div class="instruction">
                            <span class="step">3</span>
                            <span>اضغط على "ربط جهاز" وامسح الكود</span>
                        </div>
                    </div>
                </div>
            </section>

            <!-- Chat Section -->
            <section class="chat-section" id="chatSection" style="display: none;">
                <div class="chat-container">
                    <!-- Chat Header -->
                    <div class="chat-header">
                        <div class="chat-info">
                            <i class="fas fa-fighter-jet"></i>
                            <div>
                                <h3>باتريوت</h3>
                                <span class="online-status" style="color: #4CAF50;">● متصل</span>
                            </div>
                        </div>
                        <div class="chat-actions">
                            <button class="btn-icon" id="homeBtn" title="العودة للرئيسية">
                                <i class="fas fa-home"></i>
                            </button>
                            <button class="btn-icon" id="refreshBtn" title="إعادة تحميل">
                                <i class="fas fa-sync-alt"></i>
                            </button>
                            <button class="btn-icon" id="logoutBtn" title="تسجيل الخروج">
                                <i class="fas fa-sign-out-alt"></i>
                            </button>
                        </div>
                    </div>

                    <!-- Messages Area -->
                    <div class="messages-area" id="messagesArea">
                        <div class="welcome-message">
                            <i class="fas fa-comments"></i>
                            <h4>مرحباً! أنت الآن متصل بواتساب</h4>
                            <p>يمكنك الآن إرسال الرسائل باستخدام النموذج أدناه</p>
                        </div>
                    </div>

                    <!-- Send Message Form -->
                    <div class="send-form">
                        <!-- تبويبات الإرسال -->
                        <div class="send-tabs">
                            <button type="button" class="tab-btn active" data-tab="single">
                                <i class="fas fa-user"></i> رسالة فردية
                            </button>
                            <button type="button" class="tab-btn" data-tab="bulk">
                                <i class="fas fa-users"></i> رسائل جماعية
                            </button>
                            <button type="button" class="tab-btn" data-tab="file">
                                <i class="fas fa-paperclip"></i> إرسال ملف
                            </button>
                            <button type="button" class="tab-btn" data-tab="contacts">
                                <i class="fas fa-address-book"></i> جهات الاتصال
                            </button>
                        </div>

                        <!-- تبويب الرسالة الفردية -->
                        <div class="tab-content active" id="singleTab">
                            <form id="messageForm">
                                <div class="form-group">
                                    <label for="phoneNumber">رقم الهاتف (مع رمز البلد)</label>
                                    <div class="phone-input-group">
                                        <input type="tel" id="phoneNumber" placeholder="مثال: 966501234567" required>
                                        <button type="button" class="btn-select-contact" id="selectContactSingle">
                                            <i class="fas fa-address-book"></i>
                                        </button>
                                    </div>
                                </div>
                                <div class="form-group">
                                    <label for="messageText">الرسالة</label>
                                    <textarea id="messageText" placeholder="اكتب رسالتك هنا..." required></textarea>
                                </div>
                                <div class="form-group">
                                    <label for="sendDelay">زمن التأخير بين الرسائل (بالثواني)</label>
                                    <input type="number" id="sendDelay" min="1" max="60" value="3" placeholder="3">
                                    <small style="color: #666;">لتجنب حظر واتساب، يُنصح بـ 3-5 ثوانٍ</small>
                                </div>
                                <div class="form-group">
                                    <label for="attachments">المرفقات (اختياري)</label>
                                    <div class="attachment-btn" onclick="document.getElementById('attachments').click()">
                                        <i class="fas fa-paperclip"></i>
                                        <span>اضغط لإضافة مرفقات</span>
                                    </div>
                                    <input type="file" id="attachments" multiple accept="*/*" style="display: none;">
                                    <div class="attachments-list" id="attachmentsList"></div>
                                </div>
                                <button type="submit" class="send-btn" id="sendBtn">
                                    <i class="fas fa-paper-plane"></i>
                                    إرسال الرسالة
                                </button>
                            </form>
                        </div>

                        <!-- تبويب الرسائل الجماعية -->
                        <div class="tab-content" id="bulkTab">
                            <form id="bulkMessageForm">
                                <div class="form-group">
                                    <label for="bulkNumbers">أرقام الهواتف (رقم واحد في كل سطر)</label>
                                    <div class="bulk-numbers-group">
                                        <textarea id="bulkNumbers" placeholder="966501234567&#10;966507654321&#10;966512345678" rows="4" required></textarea>
                                        <div class="bulk-contacts-actions">
                                            <button type="button" class="btn-select-contacts" id="selectContactsBulk">
                                                <i class="fas fa-address-book"></i> اختيار من جهات الاتصال
                                            </button>
                                            <button type="button" class="btn-select-all-contacts" id="selectAllContacts">
                                                <i class="fas fa-users"></i> تحديد الكل
                                            </button>
                                        </div>
                                    </div>
                                </div>
                                <div class="form-group">
                                    <label for="bulkMessage">الرسالة</label>
                                    <textarea id="bulkMessage" placeholder="اكتب رسالتك هنا..." required></textarea>
                                </div>
                                <div class="form-group">
                                    <label for="bulkAttachments">المرفقات (اختياري)</label>
                                    <div class="attachment-btn" onclick="document.getElementById('bulkAttachments').click()">
                                        <i class="fas fa-paperclip"></i>
                                        <span>اضغط لإضافة مرفقات</span>
                                    </div>
                                    <input type="file" id="bulkAttachments" multiple accept="*/*" style="display: none;">
                                    <div class="attachments-list" id="bulkAttachmentsList"></div>
                                </div>
                                <div class="form-group">
                                    <label for="bulkDelay">زمن التأخير بين الرسائل (بالثواني)</label>
                                    <input type="number" id="bulkDelay" min="1" max="60" value="5" placeholder="5">
                                    <small style="color: #666;">للرسائل الجماعية، يُنصح بـ 5-10 ثوانٍ</small>
                                </div>
                                <button type="submit" class="send-btn" id="bulkSendBtn">
                                    <i class="fas fa-bullhorn"></i>
                                    إرسال للجميع
                                </button>
                            </form>
                        </div>

                        <!-- تبويب إرسال الملفات -->
                        <div class="tab-content" id="fileTab">
                            <form id="fileForm" enctype="multipart/form-data">
                                <div class="form-group">
                                    <label for="filePhoneNumber">رقم الهاتف (مع رمز البلد)</label>
                                    <div class="phone-input-group">
                                        <input type="tel" id="filePhoneNumber" placeholder="مثال: 966501234567" required>
                                        <button type="button" class="btn-select-contact" id="selectContactFile">
                                            <i class="fas fa-address-book"></i>
                                        </button>
                                    </div>
                                </div>
                                <div class="form-group">
                                    <label for="fileInput">اختر الملف</label>
                                    <div class="attachment-btn" onclick="document.getElementById('fileInput').click()">
                                        <i class="fas fa-cloud-upload-alt"></i>
                                        <span>اضغط لاختيار ملف أو اسحبه هنا</span>
                                    </div>
                                    <input type="file" id="fileInput" accept="*/*" required style="display: none;">
                                    <div id="selectedFileInfo" style="margin-top: 1rem; display: none;">
                                        <div class="attachment-item">
                                            <div class="attachment-info">
                                                <i class="fas fa-file"></i>
                                                <span id="selectedFileName"></span>
                                            </div>
                                            <button type="button" class="attachment-remove" onclick="clearSelectedFile()">
                                                <i class="fas fa-times"></i>
                                            </button>
                                        </div>
                                    </div>
                                </div>
                                <div class="form-group">
                                    <label for="fileCaption">تعليق (اختياري)</label>
                                    <input type="text" id="fileCaption" placeholder="أضف تعليق للملف...">
                                </div>
                                <button type="submit" class="send-btn file-send-btn" id="fileSendBtn">
                                    <i class="fas fa-upload"></i>
                                    إرسال الملف
                                </button>
                            </form>
                        </div>

                        <!-- تبويب جهات الاتصال -->
                        <div class="tab-content" id="contactsTab">
                            <div class="contacts-manager">
                                <div class="contacts-header">
                                    <h3><i class="fas fa-address-book"></i> إدارة جهات الاتصال</h3>
                                    <button type="button" class="btn-add-contact" id="addContactBtn">
                                        <i class="fas fa-plus"></i> إضافة جهة اتصال
                                    </button>
                                </div>

                                <!-- نموذج إضافة جهة اتصال -->
                                <div class="add-contact-form" id="addContactForm" style="display: none;">
                                    <div class="form-group">
                                        <label for="contactName">اسم جهة الاتصال</label>
                                        <input type="text" id="contactName" placeholder="مثال: أحمد محمد" required>
                                    </div>
                                    <div class="form-group">
                                        <label for="contactPhone">رقم الهاتف (مع رمز البلد)</label>
                                        <input type="tel" id="contactPhone" placeholder="مثال: 966501234567" required>
                                    </div>
                                    <div class="form-group">
                                        <label for="contactNotes">ملاحظات (اختياري)</label>
                                        <input type="text" id="contactNotes" placeholder="مثال: صديق، عميل، زميل عمل">
                                    </div>
                                    <div class="form-actions">
                                        <button type="button" class="btn-save-contact" id="saveContactBtn">
                                            <i class="fas fa-save"></i> حفظ
                                        </button>
                                        <button type="button" class="btn-cancel-contact" id="cancelContactBtn">
                                            <i class="fas fa-times"></i> إلغاء
                                        </button>
                                    </div>
                                </div>

                                <!-- قائمة جهات الاتصال -->
                                <div class="contacts-list" id="contactsList">
                                    <div class="contacts-empty" id="contactsEmpty">
                                        <i class="fas fa-address-book"></i>
                                        <p>لا توجد جهات اتصال محفوظة</p>
                                        <p>اضغط على "إضافة جهة اتصال" لبدء إضافة الأرقام</p>
                                    </div>
                                </div>

                                <!-- أدوات سريعة -->
                                <div class="contacts-tools">
                                    <button type="button" class="btn-export-contacts" id="exportContactsBtn">
                                        <i class="fas fa-download"></i> تصدير جهات الاتصال
                                    </button>
                                    <button type="button" class="btn-import-contacts" id="importContactsBtn">
                                        <i class="fas fa-upload"></i> استيراد جهات الاتصال
                                    </button>
                                    <input type="file" id="importContactsFile" accept=".json" style="display: none;">
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </section>
        </div>
    </main>

    <!-- Footer -->
    <footer class="footer">
        <div class="container">
            <div class="footer-content">
                <div class="footer-info">
                    <p>&copy; 2025 باتريوت - مراسل واتساب المتقدم</p>
                    <p>جميع الحقوق محفوظة لـ فارس الحربي</p>
                </div>
                <div class="footer-logo">
                    <i class="fas fa-fighter-jet"></i>
                    <span>باتريوت</span>
                </div>
            </div>
        </div>
    </footer>

    <!-- Loading Overlay -->
    <div class="loading-overlay" id="loadingOverlay">
        <div class="loading-spinner">
            <i class="fas fa-spinner fa-spin"></i>
            <span>جاري المعالجة...</span>
        </div>
    </div>

    <!-- Notification -->
    <div class="notification" id="notification">
        <div class="notification-content">
            <i class="notification-icon"></i>
            <span class="notification-text"></span>
        </div>
    </div>

    <!-- Scripts -->
    <script src="/socket.io/socket.io.js"></script>
    <script src="script.js"></script>
</body>
</html>
