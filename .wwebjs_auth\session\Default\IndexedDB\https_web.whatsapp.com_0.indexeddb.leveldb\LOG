2025/08/24-01:33:42.391 3c8 Creating DB C:\Users\<USER>\Desktop\البرنامج\.wwebjs_auth\session\Default\IndexedDB\https_web.whatsapp.com_0.indexeddb.leveldb since it was missing.
2025/08/24-01:33:42.395 3c8 Reusing MANIFEST C:\Users\<USER>\Desktop\البرنامج\.wwebjs_auth\session\Default\IndexedDB\https_web.whatsapp.com_0.indexeddb.leveldb/MANIFEST-000001
2025/08/24-01:33:42.760 374c Level-0 table #5: started
2025/08/24-01:33:42.764 374c Level-0 table #5: 25176 bytes OK
2025/08/24-01:33:42.766 374c Delete type=0 #3
2025/08/24-01:33:42.771 374c Manual compaction at level-0 from '\x00\x03\x00\x00\x00' @ 72057594037927935 : 1 .. '\x00\x04\x00\x00\x00' @ 0 : 0; will stop at (end)
2025/08/24-01:33:42.772 3c8 Level-0 table #7: started
2025/08/24-01:33:42.780 3c8 Level-0 table #7: 1114 bytes OK
2025/08/24-01:33:42.781 3c8 Delete type=0 #4
2025/08/24-01:33:42.782 3c8 Manual compaction at level-0 from '\x00\x04\x00\x00\x00' @ 72057594037927935 : 1 .. '\x00\x05\x00\x00\x00' @ 0 : 0; will stop at (end)
2025/08/24-01:33:42.782 3c8 Manual compaction at level-1 from '\x00\x04\x00\x00\x00' @ 72057594037927935 : 1 .. '\x00\x05\x00\x00\x00' @ 0 : 0; will stop at '\x00\x04\x00\x00\x05' @ 1538 : 0
2025/08/24-01:33:42.782 3c8 Compacting 1@1 + 1@2 files
2025/08/24-01:33:42.785 3c8 Generated table #8@1: 437 keys, 12825 bytes
2025/08/24-01:33:42.785 3c8 Compacted 1@1 + 1@2 files => 12825 bytes
2025/08/24-01:33:42.787 3c8 compacted to: files[ 0 0 1 0 0 0 0 ]
2025/08/24-01:33:42.788 3c8 Delete type=2 #7
2025/08/24-01:33:42.788 3c8 Manual compaction at level-1 from '\x00\x04\x00\x00\x05' @ 1538 : 0 .. '\x00\x05\x00\x00\x00' @ 0 : 0; will stop at (end)
2025/08/24-01:33:42.789 f00 Level-0 table #10: started
2025/08/24-01:33:42.802 f00 Level-0 table #10: 1493 bytes OK
2025/08/24-01:33:42.804 f00 Delete type=2 #5
2025/08/24-01:33:42.804 f00 Delete type=0 #6
2025/08/24-01:33:42.804 f00 Manual compaction at level-0 from '\x00\x05\x00\x00\x00' @ 72057594037927935 : 1 .. '\x00\x06\x00\x00\x00' @ 0 : 0; will stop at (end)
2025/08/24-01:33:42.804 f00 Manual compaction at level-1 from '\x00\x05\x00\x00\x00' @ 72057594037927935 : 1 .. '\x00\x06\x00\x00\x00' @ 0 : 0; will stop at '\x00\x0e\x00\x00\xc8\x04\x00k\x00e\x00y\x00s' @ 1587 : 1
2025/08/24-01:33:42.804 f00 Compacting 1@1 + 1@2 files
2025/08/24-01:33:42.807 f00 Generated table #11@1: 487 keys, 13555 bytes
2025/08/24-01:33:42.807 f00 Compacted 1@1 + 1@2 files => 13555 bytes
2025/08/24-01:33:42.809 f00 compacted to: files[ 0 0 1 0 0 0 0 ]
2025/08/24-01:33:42.809 f00 Delete type=2 #10
2025/08/24-01:33:42.809 f00 Manual compaction at level-1 from '\x00\x0e\x00\x00\xc8\x04\x00k\x00e\x00y\x00s' @ 1587 : 1 .. '\x00\x06\x00\x00\x00' @ 0 : 0; will stop at (end)
2025/08/24-01:33:42.810 f00 Level-0 table #13: started
2025/08/24-01:33:42.818 f00 Level-0 table #13: 3516 bytes OK
2025/08/24-01:33:42.823 f00 Delete type=2 #8
2025/08/24-01:33:42.823 f00 Delete type=0 #9
2025/08/24-01:33:42.823 f00 Manual compaction at level-0 from '\x00\x06\x00\x00\x00' @ 72057594037927935 : 1 .. '\x00\x07\x00\x00\x00' @ 0 : 0; will stop at (end)
2025/08/24-01:33:42.823 f00 Manual compaction at level-1 from '\x00\x06\x00\x00\x00' @ 72057594037927935 : 1 .. '\x00\x07\x00\x00\x00' @ 0 : 0; will stop at '\x00\x10\x00\x00\x05' @ 1816 : 1
2025/08/24-01:33:42.823 f00 Compacting 1@1 + 1@2 files
2025/08/24-01:33:42.826 f00 Generated table #14@1: 658 keys, 16772 bytes
2025/08/24-01:33:42.826 f00 Compacted 1@1 + 1@2 files => 16772 bytes
2025/08/24-01:33:42.828 f00 compacted to: files[ 0 0 1 0 0 0 0 ]
2025/08/24-01:33:42.828 f00 Delete type=2 #13
2025/08/24-01:33:42.828 f00 Manual compaction at level-1 from '\x00\x10\x00\x00\x05' @ 1816 : 1 .. '\x00\x07\x00\x00\x00' @ 0 : 0; will stop at (end)
2025/08/24-01:33:42.829 ef4 Level-0 table #16: started
2025/08/24-01:33:42.836 ef4 Level-0 table #16: 252 bytes OK
2025/08/24-01:33:42.838 ef4 Delete type=2 #11
2025/08/24-01:33:42.838 ef4 Delete type=0 #12
2025/08/24-01:33:42.839 ef4 Manual compaction at level-0 from '\x00\x07\x00\x00\x00' @ 72057594037927935 : 1 .. '\x00\x08\x00\x00\x00' @ 0 : 0; will stop at (end)
2025/08/24-01:33:42.839 ef4 Manual compaction at level-1 from '\x00\x07\x00\x00\x00' @ 72057594037927935 : 1 .. '\x00\x08\x00\x00\x00' @ 0 : 0; will stop at '\x00\x07\x00\x00\x05' @ 1836 : 0
2025/08/24-01:33:42.839 ef4 Compacting 1@1 + 1@2 files
2025/08/24-01:33:42.842 ef4 Generated table #17@1: 652 keys, 16769 bytes
2025/08/24-01:33:42.842 ef4 Compacted 1@1 + 1@2 files => 16769 bytes
2025/08/24-01:33:42.844 ef4 compacted to: files[ 0 0 1 0 0 0 0 ]
2025/08/24-01:33:42.844 ef4 Delete type=2 #16
2025/08/24-01:33:42.844 ef4 Manual compaction at level-1 from '\x00\x07\x00\x00\x05' @ 1836 : 0 .. '\x00\x08\x00\x00\x00' @ 0 : 0; will stop at (end)
2025/08/24-01:33:42.845 f00 Level-0 table #19: started
2025/08/24-01:33:42.853 f00 Level-0 table #19: 249 bytes OK
2025/08/24-01:33:42.855 f00 Delete type=2 #14
2025/08/24-01:33:42.855 f00 Delete type=0 #15
2025/08/24-01:33:42.855 f00 Manual compaction at level-0 from '\x00\x08\x00\x00\x00' @ 72057594037927935 : 1 .. '\x00\x09\x00\x00\x00' @ 0 : 0; will stop at (end)
2025/08/24-01:33:42.855 f00 Manual compaction at level-1 from '\x00\x08\x00\x00\x00' @ 72057594037927935 : 1 .. '\x00\x09\x00\x00\x00' @ 0 : 0; will stop at '\x00\x08\x00\x00\x05' @ 1842 : 0
2025/08/24-01:33:42.855 f00 Compacting 1@1 + 1@2 files
2025/08/24-01:33:42.858 f00 Generated table #20@1: 646 keys, 16674 bytes
2025/08/24-01:33:42.858 f00 Compacted 1@1 + 1@2 files => 16674 bytes
2025/08/24-01:33:42.858 f00 compacted to: files[ 0 0 1 0 0 0 0 ]
2025/08/24-01:33:42.859 f00 Delete type=2 #19
2025/08/24-01:33:42.859 f00 Manual compaction at level-1 from '\x00\x08\x00\x00\x05' @ 1842 : 0 .. '\x00\x09\x00\x00\x00' @ 0 : 0; will stop at (end)
2025/08/24-01:33:42.859 f00 Level-0 table #22: started
2025/08/24-01:33:42.867 f00 Level-0 table #22: 251 bytes OK
2025/08/24-01:33:42.868 f00 Delete type=2 #17
2025/08/24-01:33:42.868 f00 Delete type=0 #18
2025/08/24-01:33:42.868 f00 Manual compaction at level-0 from '\x00\x09\x00\x00\x00' @ 72057594037927935 : 1 .. '\x00\x0a\x00\x00\x00' @ 0 : 0; will stop at (end)
2025/08/24-01:33:42.868 f00 Manual compaction at level-1 from '\x00\x09\x00\x00\x00' @ 72057594037927935 : 1 .. '\x00\x0a\x00\x00\x00' @ 0 : 0; will stop at '\x00\x09\x00\x00\x05' @ 1848 : 0
2025/08/24-01:33:42.868 f00 Compacting 1@1 + 1@2 files
2025/08/24-01:33:42.870 f00 Generated table #23@1: 640 keys, 15806 bytes
2025/08/24-01:33:42.870 f00 Compacted 1@1 + 1@2 files => 15806 bytes
2025/08/24-01:33:42.871 f00 compacted to: files[ 0 0 1 0 0 0 0 ]
2025/08/24-01:33:42.872 f00 Delete type=2 #22
2025/08/24-01:33:42.872 f00 Manual compaction at level-1 from '\x00\x09\x00\x00\x05' @ 1848 : 0 .. '\x00\x0a\x00\x00\x00' @ 0 : 0; will stop at (end)
2025/08/24-01:33:42.872 f00 Level-0 table #25: started
2025/08/24-01:33:42.879 f00 Level-0 table #25: 249 bytes OK
2025/08/24-01:33:42.881 f00 Delete type=2 #20
2025/08/24-01:33:42.881 f00 Delete type=0 #21
2025/08/24-01:33:42.881 f00 Manual compaction at level-0 from '\x00\x0d\x00\x00\x00' @ 72057594037927935 : 1 .. '\x00\x0e\x00\x00\x00' @ 0 : 0; will stop at (end)
2025/08/24-01:33:42.881 f00 Manual compaction at level-1 from '\x00\x0d\x00\x00\x00' @ 72057594037927935 : 1 .. '\x00\x0e\x00\x00\x00' @ 0 : 0; will stop at '\x00\x0d\x00\x00\x05' @ 1854 : 0
2025/08/24-01:33:42.881 f00 Compacting 1@1 + 1@2 files
2025/08/24-01:33:42.884 f00 Generated table #26@1: 634 keys, 15549 bytes
2025/08/24-01:33:42.884 f00 Compacted 1@1 + 1@2 files => 15549 bytes
2025/08/24-01:33:42.885 f00 compacted to: files[ 0 0 1 0 0 0 0 ]
2025/08/24-01:33:42.885 f00 Delete type=2 #25
2025/08/24-01:33:42.885 f00 Manual compaction at level-1 from '\x00\x0d\x00\x00\x05' @ 1854 : 0 .. '\x00\x0e\x00\x00\x00' @ 0 : 0; will stop at (end)
2025/08/24-01:33:42.885 f00 Level-0 table #28: started
2025/08/24-01:33:42.897 f00 Level-0 table #28: 249 bytes OK
2025/08/24-01:33:42.903 f00 Delete type=2 #23
2025/08/24-01:33:42.903 f00 Delete type=0 #24
2025/08/24-01:33:42.903 f00 Manual compaction at level-0 from '\x00\x0b\x00\x00\x00' @ 72057594037927935 : 1 .. '\x00\x0c\x00\x00\x00' @ 0 : 0; will stop at (end)
2025/08/24-01:33:42.903 f00 Manual compaction at level-1 from '\x00\x0b\x00\x00\x00' @ 72057594037927935 : 1 .. '\x00\x0c\x00\x00\x00' @ 0 : 0; will stop at '\x00\x0b\x00\x00\x05' @ 1860 : 0
2025/08/24-01:33:42.903 f00 Compacting 1@1 + 1@2 files
2025/08/24-01:33:42.906 f00 Generated table #29@1: 628 keys, 15457 bytes
2025/08/24-01:33:42.906 f00 Compacted 1@1 + 1@2 files => 15457 bytes
2025/08/24-01:33:42.907 f00 compacted to: files[ 0 0 1 0 0 0 0 ]
2025/08/24-01:33:42.907 f00 Delete type=2 #28
2025/08/24-01:33:42.907 f00 Manual compaction at level-1 from '\x00\x0b\x00\x00\x05' @ 1860 : 0 .. '\x00\x0c\x00\x00\x00' @ 0 : 0; will stop at (end)
2025/08/24-01:33:42.908 ef4 Level-0 table #31: started
2025/08/24-01:33:42.915 ef4 Level-0 table #31: 249 bytes OK
2025/08/24-01:33:42.920 ef4 Delete type=2 #26
2025/08/24-01:33:42.920 ef4 Delete type=0 #27
2025/08/24-01:33:42.920 ef4 Manual compaction at level-0 from '\x00\x0c\x00\x00\x00' @ 72057594037927935 : 1 .. '\x00\x0d\x00\x00\x00' @ 0 : 0; will stop at (end)
2025/08/24-01:33:42.920 ef4 Manual compaction at level-1 from '\x00\x0c\x00\x00\x00' @ 72057594037927935 : 1 .. '\x00\x0d\x00\x00\x00' @ 0 : 0; will stop at '\x00\x0c\x00\x00\x05' @ 1866 : 0
2025/08/24-01:33:42.920 ef4 Compacting 1@1 + 1@2 files
2025/08/24-01:33:42.922 ef4 Generated table #32@1: 622 keys, 15325 bytes
2025/08/24-01:33:42.922 ef4 Compacted 1@1 + 1@2 files => 15325 bytes
2025/08/24-01:33:42.923 ef4 compacted to: files[ 0 0 1 0 0 0 0 ]
2025/08/24-01:33:42.923 ef4 Delete type=2 #31
2025/08/24-01:33:42.924 ef4 Manual compaction at level-1 from '\x00\x0c\x00\x00\x05' @ 1866 : 0 .. '\x00\x0d\x00\x00\x00' @ 0 : 0; will stop at (end)
2025/08/24-01:33:42.924 f00 Level-0 table #34: started
2025/08/24-01:33:42.932 f00 Level-0 table #34: 251 bytes OK
2025/08/24-01:33:42.933 f00 Delete type=2 #29
2025/08/24-01:33:42.933 f00 Delete type=0 #30
2025/08/24-01:33:42.933 f00 Manual compaction at level-0 from '\x00\x0a\x00\x00\x00' @ 72057594037927935 : 1 .. '\x00\x0b\x00\x00\x00' @ 0 : 0; will stop at (end)
2025/08/24-01:33:42.933 f00 Manual compaction at level-1 from '\x00\x0a\x00\x00\x00' @ 72057594037927935 : 1 .. '\x00\x0b\x00\x00\x00' @ 0 : 0; will stop at '\x00\x0a\x00\x00\x05' @ 1872 : 0
2025/08/24-01:33:42.933 f00 Compacting 1@1 + 1@2 files
2025/08/24-01:33:42.936 f00 Generated table #35@1: 616 keys, 15301 bytes
2025/08/24-01:33:42.936 f00 Compacted 1@1 + 1@2 files => 15301 bytes
2025/08/24-01:33:42.937 f00 compacted to: files[ 0 0 1 0 0 0 0 ]
2025/08/24-01:33:42.937 f00 Delete type=2 #34
2025/08/24-01:33:42.937 f00 Manual compaction at level-1 from '\x00\x0a\x00\x00\x05' @ 1872 : 0 .. '\x00\x0b\x00\x00\x00' @ 0 : 0; will stop at (end)
2025/08/24-01:33:42.938 ef4 Level-0 table #37: started
2025/08/24-01:33:42.945 ef4 Level-0 table #37: 1191 bytes OK
2025/08/24-01:33:42.946 ef4 Delete type=2 #32
2025/08/24-01:33:42.946 ef4 Delete type=0 #33
2025/08/24-01:33:42.947 ef4 Manual compaction at level-0 from '\x00\x10\x00\x00\x00' @ 72057594037927935 : 1 .. '\x00\x11\x00\x00\x00' @ 0 : 0; will stop at (end)
2025/08/24-01:33:42.947 3c8 Manual compaction at level-1 from '\x00\x10\x00\x00\x00' @ 72057594037927935 : 1 .. '\x00\x11\x00\x00\x00' @ 0 : 0; will stop at '\x00\x10\x00\x00\x05' @ 1992 : 0
2025/08/24-01:33:42.947 3c8 Compacting 1@1 + 1@2 files
2025/08/24-01:33:42.950 3c8 Generated table #38@1: 496 keys, 13550 bytes
2025/08/24-01:33:42.950 3c8 Compacted 1@1 + 1@2 files => 13550 bytes
2025/08/24-01:33:42.950 3c8 compacted to: files[ 0 0 1 0 0 0 0 ]
2025/08/24-01:33:42.951 3c8 Delete type=2 #37
2025/08/24-01:33:42.951 3c8 Manual compaction at level-1 from '\x00\x10\x00\x00\x05' @ 1992 : 0 .. '\x00\x11\x00\x00\x00' @ 0 : 0; will stop at (end)
