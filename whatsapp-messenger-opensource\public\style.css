/* إعدادات عامة */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Cairo', sans-serif;
    background: linear-gradient(135deg, #0a0a0a 0%, #1a1a1a 50%, #000000 100%);
    background-attachment: fixed;
    min-height: 100vh;
    color: #ffffff;
    direction: rtl;
    overflow-x: hidden;
    /* إخفاء شريط التمرير للصفحة الرئيسية */
    -ms-overflow-style: none; /* IE and Edge */
    scrollbar-width: none;    /* Firefox */
}

/* السماح بالتمرير */

/* إخفاء شريط التمرير في المتصفحات المعتمدة على WebKit */
body::-webkit-scrollbar {
    width: 0;
    height: 0;
}

.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
}

/* Header */
.header {
    background: rgba(0, 0, 0, 0.95);
    backdrop-filter: blur(20px);
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.8);
    position: sticky;
    top: 0;
    z-index: 100;
    transition: all 0.3s ease;
}

/* الهيدر مرئي دائماً */

.header-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 1rem 0;
}

.logo {
    display: flex;
    align-items: center;
    gap: 10px;
}

.logo i {
    font-size: 2.5rem;
    color: #ff6b35;
    text-shadow: 0 0 20px rgba(255, 107, 53, 0.5);
}

.logo h1 {
    font-size: 1.8rem;
    font-weight: 700;
    color: #ffffff;
    text-shadow: 0 0 10px rgba(255, 255, 255, 0.3);
}

.version {
    background: linear-gradient(45deg, #ff6b35, #f7931e);
    color: white;
    padding: 4px 12px;
    border-radius: 20px;
    font-size: 0.8rem;
    font-weight: 600;
    box-shadow: 0 4px 15px rgba(255, 107, 53, 0.3);
}

.header-actions {
    display: none; /* مخفي افتراضياً حتى يتم الاتصال */
    align-items: center;
    gap: 1rem;
}

.header-actions.show {
    display: flex;
}

.btn-header {
    background: rgba(255, 255, 255, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.2);
    color: #ffffff;
    padding: 0.5rem 1rem;
    border-radius: 10px;
    font-size: 0.9rem;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    gap: 0.5rem;
    backdrop-filter: blur(10px);
    font-family: 'Cairo', sans-serif;
}

.btn-header:hover {
    background: rgba(255, 255, 255, 0.2);
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.3);
}

.btn-logout {
    background: rgba(220, 53, 69, 0.2);
    border-color: rgba(220, 53, 69, 0.3);
}

.btn-logout:hover {
    background: rgba(220, 53, 69, 0.3);
}



.form-group {
    margin-bottom: 1.5rem;
}

.form-group label {
    display: block;
    margin-bottom: 0.5rem;
    color: #ffffff;
    font-weight: 600;
}

.form-group textarea {
    width: 100%;
    padding: 1rem;
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: 10px;
    background: rgba(255, 255, 255, 0.1);
    color: #ffffff !important;
    font-family: 'Cairo', sans-serif;
    resize: vertical;
    backdrop-filter: blur(10px);
}

.form-group textarea::placeholder {
    color: rgba(255, 255, 255, 0.6);
}

.form-group textarea:focus {
    outline: none;
    border-color: #ff6b35;
    box-shadow: 0 0 20px rgba(255, 107, 53, 0.3);
}

/* تنسيق خاص لمربعات الإدخال في القسم الرئيسي */
.main-content .form-group input {
    width: 100%;
    padding: 1rem;
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: 10px;
    background: rgba(255, 255, 255, 0.1);
    color: #ffffff !important;
    font-family: 'Cairo', sans-serif;
    backdrop-filter: blur(10px);
}

.main-content .form-group input::placeholder {
    color: rgba(255, 255, 255, 0.6);
}

.main-content .form-group input:focus {
    outline: none;
    border-color: #ff6b35;
    box-shadow: 0 0 20px rgba(255, 107, 53, 0.3);
}

/* تنسيق المرفقات */
.attachments-container {
    border: 2px dashed rgba(255, 255, 255, 0.3);
    border-radius: 10px;
    padding: 1rem;
    text-align: center;
    background: rgba(255, 255, 255, 0.05);
    transition: all 0.3s ease;
}

.attachments-container:hover {
    border-color: #ff6b35;
    background: rgba(255, 107, 53, 0.1);
}

.attachments-container input[type="file"] {
    width: 100%;
    padding: 1rem;
    border: none;
    background: transparent;
    color: #ffffff;
    cursor: pointer;
}

.attachments-list {
    margin-top: 1rem;
    display: flex;
    flex-wrap: wrap;
    gap: 0.5rem;
}

.attachment-item {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    background: rgba(255, 107, 53, 0.2);
    padding: 0.5rem 1rem;
    border-radius: 20px;
    color: #ffffff;
    font-size: 0.9rem;
}

.attachment-item .remove-attachment {
    background: none;
    border: none;
    color: #ffffff;
    cursor: pointer;
    font-size: 1rem;
    padding: 0;
    margin-left: 0.5rem;
}

.attachment-item .remove-attachment:hover {
    color: #ff6b35;
}

/* تنسيق حقل الزمن */
.main-content .form-group input[type="number"] {
    width: 100%;
    padding: 1rem;
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: 10px;
    background: rgba(255, 255, 255, 0.1);
    color: #ffffff !important;
    font-family: 'Cairo', sans-serif;
    backdrop-filter: blur(10px);
    text-align: center;
}

.main-content .form-group small {
    display: block;
    margin-top: 0.5rem;
    font-size: 0.85rem;
    text-align: center;
}

.form-actions {
    display: flex;
    gap: 1rem;
    justify-content: flex-end;
}

.btn-cancel, .btn-send {
    padding: 0.75rem 1.5rem;
    border: none;
    border-radius: 10px;
    cursor: pointer;
    font-family: 'Cairo', sans-serif;
    font-weight: 600;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.btn-cancel {
    background: rgba(108, 117, 125, 0.2);
    color: #ffffff;
    border: 1px solid rgba(108, 117, 125, 0.3);
}

.btn-cancel:hover {
    background: rgba(108, 117, 125, 0.3);
    transform: translateY(-2px);
}

.btn-send {
    background: linear-gradient(45deg, #ff6b35, #f7931e);
    color: #ffffff;
    box-shadow: 0 4px 15px rgba(255, 107, 53, 0.3);
}

.btn-send:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(255, 107, 53, 0.4);
}

.status-indicator {
    display: flex;
    align-items: center;
    gap: 8px;
    background: rgba(0, 0, 0, 0.3);
    padding: 8px 16px;
    border-radius: 25px;
    border: 1px solid rgba(255, 255, 255, 0.2);
    backdrop-filter: blur(10px);
}

.status-dot {
    width: 12px;
    height: 12px;
    border-radius: 50%;
    background: #ffc107;
    animation: pulse 2s infinite;
}

.status-dot.connected {
    background: #4CAF50;
    animation: none;
}

.status-dot.disconnected {
    background: #dc3545;
    animation: pulse 2s infinite;
}

@keyframes pulse {
    0% { opacity: 1; }
    50% { opacity: 0.5; }
    100% { opacity: 1; }
}

/* Main Content */
.main {
    padding: 2rem 0;
    min-height: calc(100vh - 200px);
}

/* Welcome Section */
.welcome-section {
    display: flex;
    justify-content: center;
    align-items: center;
    /* اجعل الكارت في منتصف الشاشة تماماً */
    min-height: calc(100vh - 160px); /* تقريباً ارتفاع الهيدر والفوتر معاً */
    padding: 2rem 0;
}

.welcome-card {
    background: rgba(0, 0, 0, 0.8);
    backdrop-filter: blur(20px);
    border: 1px solid rgba(255, 255, 255, 0.1);
    border-radius: 25px;
    padding: 3rem;
    text-align: center;
    box-shadow: 0 20px 60px rgba(0, 0, 0, 0.8);
    max-width: 600px;
    width: 100%;
    position: relative;
    overflow: hidden;
    margin: 0 auto;
}

.welcome-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(45deg, rgba(255, 107, 53, 0.1) 0%, transparent 50%);
    pointer-events: none;
}

.welcome-icon {
    font-size: 4rem;
    color: #ff6b35;
    margin-bottom: 1rem;
    text-shadow: 0 0 30px rgba(255, 107, 53, 0.5);
}

.welcome-card h2 {
    font-size: 2.5rem;
    font-weight: 700;
    margin-bottom: 1rem;
    background: linear-gradient(45deg, #ff6b35, #f7931e);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.welcome-card p {
    font-size: 1.2rem;
    color: #666;
    margin-bottom: 2rem;
}

.features {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
    gap: 1rem;
    margin-top: 2rem;
}

.feature {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 8px;
    padding: 1rem;
    background: rgba(255, 107, 53, 0.1);
    border-radius: 15px;
    border: 1px solid rgba(255, 107, 53, 0.2);
}

.feature i {
    font-size: 1.5rem;
    color: #ff6b35;
}

.feature span {
    font-weight: 600;
    color: #333;
}

/* زر البدء */
.welcome-actions {
    margin-top: 3rem;
    text-align: center;
}

.btn-start {
    background: linear-gradient(45deg, #ff6b35, #f7931e);
    color: white;
    border: none;
    padding: 1.2rem 2.5rem;
    border-radius: 50px;
    font-size: 1.2rem;
    font-weight: 700;
    font-family: 'Cairo', sans-serif;
    cursor: pointer;
    transition: all 0.3s ease;
    box-shadow: 0 8px 25px rgba(255, 107, 53, 0.3);
    text-transform: none;
    display: inline-flex;
    align-items: center;
    gap: 0.8rem;
}

.btn-start:hover {
    transform: translateY(-3px);
    box-shadow: 0 12px 35px rgba(255, 107, 53, 0.4);
    background: linear-gradient(45deg, #f7931e, #ff6b35);
}

.btn-start:active {
    transform: translateY(-1px);
}

.btn-start i {
    font-size: 1.3rem;
}

/* QR Section */
.qr-section {
    display: flex;
    justify-content: center;
    align-items: center;
    min-height: calc(100vh - 160px); /* نفس ارتفاع صفحة الترحيب */
    padding: 2rem 0;
}

.qr-card {
    background: rgba(0, 0, 0, 0.8);
    backdrop-filter: blur(20px);
    border: 1px solid rgba(255, 255, 255, 0.1);
    border-radius: 25px;
    padding: 3rem;
    text-align: center;
    box-shadow: 0 20px 60px rgba(0, 0, 0, 0.8);
    max-width: 600px;
    width: 100%;
    position: relative;
    overflow: hidden;
    margin: 0 auto;
}

.qr-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(45deg, rgba(255, 107, 53, 0.1) 0%, transparent 50%);
    pointer-events: none;
}

.qr-header h3 {
    font-size: 2.5rem;
    font-weight: 700;
    margin-bottom: 1rem;
    background: linear-gradient(45deg, #ff6b35, #f7931e);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    position: relative;
    z-index: 1;
}

.qr-header p {
    color: #666;
    margin-bottom: 2rem;
    font-size: 1.2rem;
    position: relative;
    z-index: 1;
}

.qr-container {
    margin: 2rem 0;
    display: flex;
    justify-content: center;
}

.qr-placeholder {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 1rem;
    padding: 3rem;
    border: 2px dashed rgba(255, 107, 53, 0.3);
    border-radius: 20px;
    color: #666;
    background: rgba(255, 107, 53, 0.05);
}

.qr-placeholder i {
    font-size: 4rem;
    color: #ff6b35;
    text-shadow: 0 0 20px rgba(255, 107, 53, 0.5);
}

.qr-placeholder span {
    font-size: 1.2rem;
    font-weight: 600;
}

#qrImage {
    max-width: 280px;
    border-radius: 20px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
    border: 3px solid rgba(255, 107, 53, 0.2);
}

.qr-instructions {
    text-align: right;
    margin-top: 2rem;
}

.instruction {
    display: flex;
    align-items: center;
    gap: 1rem;
    margin-bottom: 1rem;
    padding: 1.2rem;
    background: rgba(255, 107, 53, 0.1);
    border: 1px solid rgba(255, 107, 53, 0.2);
    border-radius: 15px;
    color: #333;
    font-weight: 600;
    font-size: 1rem;
    transition: all 0.3s ease;
}

.instruction:hover {
    background: rgba(255, 107, 53, 0.15);
    border-color: rgba(255, 107, 53, 0.3);
}

.step {
    background: linear-gradient(45deg, #ff6b35, #f7931e);
    color: white;
    width: 35px;
    height: 35px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: 700;
    font-size: 1rem;
    box-shadow: 0 4px 15px rgba(255, 107, 53, 0.4);
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
}

/* Chat Section */
.chat-section {
    padding: 2rem;
    height: calc(100vh - 140px);
}

.chat-container {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    border-radius: 20px;
    box-shadow: 0 10px 40px rgba(0, 0, 0, 0.1);
    overflow: hidden;
    max-width: 1400px;
    margin: 0 auto;
    height: 100%;
    display: grid;
    grid-template-columns: 1fr 400px;
    grid-template-rows: auto 1fr;
    grid-template-areas:
        "header header"
        "messages form";
}

.chat-header {
    background: linear-gradient(45deg, #ff6b35, #f7931e);
    color: white;
    padding: 1.5rem;
    display: flex;
    justify-content: space-between;
    align-items: center;
    grid-area: header;
}

.chat-info {
    display: flex;
    align-items: center;
    gap: 1rem;
}

.chat-info i {
    font-size: 2rem;
    color: #ff6b35;
    text-shadow: 0 0 10px rgba(255, 107, 53, 0.5);
}

.chat-info h3 {
    font-size: 1.3rem;
    font-weight: 600;
}

.online-status {
    font-size: 0.9rem;
    opacity: 0.9;
}

.chat-actions {
    display: flex;
    gap: 0.5rem;
}

.btn-icon {
    background: rgba(255, 255, 255, 0.2);
    border: none;
    color: white;
    width: 40px;
    height: 40px;
    border-radius: 50%;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    justify-content: center;
}

.btn-icon:hover {
    background: rgba(255, 255, 255, 0.3);
    transform: scale(1.1);
}

.messages-area {
    padding: 2rem;
    overflow-y: auto;
    grid-area: messages;
    background: #f8f9fa;
    border-right: 1px solid #e0e0e0;
}

.welcome-message {
    text-align: center;
    color: #666;
}

.welcome-message i {
    font-size: 3rem;
    color: #ff6b35;
    margin-bottom: 1rem;
}

.welcome-message h4 {
    font-size: 1.3rem;
    margin-bottom: 0.5rem;
    color: #333;
}

.send-form {
    padding: 2rem;
    background: rgba(0, 0, 0, 0.02);
    grid-area: form;
    overflow-y: auto;
    border-left: 1px solid rgba(0, 0, 0, 0.1);
}

.form-group {
    margin-bottom: 1.5rem;
}

.form-group label {
    display: block;
    margin-bottom: 0.5rem;
    font-weight: 600;
    color: #333;
}

.form-group input,
.form-group textarea {
    width: 100%;
    padding: 1rem;
    border: 2px solid #e0e0e0;
    border-radius: 10px;
    font-family: inherit;
    font-size: 1rem;
    transition: all 0.3s ease;
    background: white !important;
    color: #333 !important;
    box-sizing: border-box;
}

.form-group input::placeholder,
.form-group textarea::placeholder {
    color: #999 !important;
    opacity: 1;
}

.form-group input:focus,
.form-group textarea:focus {
    color: #333 !important;
}

/* إصلاح إضافي لضمان ظهور النص */
input, textarea, select {
    color: #333 !important;
    background-color: white !important;
}

input::placeholder, textarea::placeholder {
    color: #999 !important;
}

/* تأكيد ظهور النص في التبويبات */
.tab-content input,
.tab-content textarea {
    color: #333 !important;
    background: white !important;
}

.tab-content input:focus,
.tab-content textarea:focus {
    color: #333 !important;
}

/* تصميم إدارة جهات الاتصال */
.contacts-manager {
    padding: 20px;
    background: #f8f9fa;
    border-radius: 10px;
    margin-top: 20px;
}

.contacts-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
    padding-bottom: 15px;
    border-bottom: 2px solid #e9ecef;
}

.contacts-header h3 {
    color: #2c3e50;
    margin: 0;
    font-size: 1.4rem;
}

.btn-add-contact {
    background: linear-gradient(135deg, #28a745, #20c997);
    color: white;
    border: none;
    padding: 10px 20px;
    border-radius: 8px;
    cursor: pointer;
    font-weight: 600;
    transition: all 0.3s ease;
}

.btn-add-contact:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(40, 167, 69, 0.3);
}

/* نموذج إضافة جهة اتصال */
.add-contact-form {
    background: white;
    padding: 20px;
    border-radius: 10px;
    margin-bottom: 20px;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    border-left: 4px solid #28a745;
}

.form-actions {
    display: flex;
    gap: 10px;
    margin-top: 15px;
}

.btn-save-contact {
    background: linear-gradient(135deg, #007bff, #0056b3);
    color: white;
    border: none;
    padding: 10px 20px;
    border-radius: 6px;
    cursor: pointer;
    font-weight: 600;
    transition: all 0.3s ease;
}

.btn-cancel-contact {
    background: linear-gradient(135deg, #6c757d, #5a6268);
    color: white;
    border: none;
    padding: 10px 20px;
    border-radius: 6px;
    cursor: pointer;
    font-weight: 600;
    transition: all 0.3s ease;
}

.btn-save-contact:hover,
.btn-cancel-contact:hover {
    transform: translateY(-1px);
    box-shadow: 0 2px 8px rgba(0,0,0,0.2);
}

/* قائمة جهات الاتصال */
.contacts-list {
    background: white;
    border-radius: 10px;
    overflow: hidden;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
}

.contacts-empty {
    text-align: center;
    padding: 40px 20px;
    color: #6c757d;
}

.contacts-empty i {
    font-size: 3rem;
    margin-bottom: 15px;
    color: #dee2e6;
}

.contact-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 15px 20px;
    border-bottom: 1px solid #e9ecef;
    transition: background-color 0.3s ease;
}

.contact-item:hover {
    background-color: #f8f9fa;
}

.contact-item:last-child {
    border-bottom: none;
}

.contact-info {
    flex: 1;
}

.contact-name {
    font-weight: 600;
    color: #2c3e50;
    margin-bottom: 5px;
}

.contact-phone {
    color: #007bff;
    font-family: 'Courier New', monospace;
    margin-bottom: 3px;
}

.contact-notes {
    color: #6c757d;
    font-size: 0.9rem;
    font-style: italic;
}

.contact-actions {
    display: flex;
    gap: 8px;
}

.btn-contact-action {
    padding: 6px 12px;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    font-size: 0.9rem;
    transition: all 0.3s ease;
}

.btn-edit-contact {
    background: #ffc107;
    color: #212529;
}

.btn-delete-contact {
    background: #dc3545;
    color: white;
}

.btn-contact-action:hover {
    transform: translateY(-1px);
    box-shadow: 0 2px 6px rgba(0,0,0,0.2);
}

/* أدوات جهات الاتصال */
.contacts-tools {
    display: flex;
    gap: 10px;
    margin-top: 20px;
    justify-content: center;
}

.btn-export-contacts,
.btn-import-contacts {
    background: linear-gradient(135deg, #6f42c1, #5a2d91);
    color: white;
    border: none;
    padding: 10px 20px;
    border-radius: 8px;
    cursor: pointer;
    font-weight: 600;
    transition: all 0.3s ease;
}

.btn-export-contacts:hover,
.btn-import-contacts:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(111, 66, 193, 0.3);
}

/* أزرار اختيار جهات الاتصال */
.phone-input-group {
    display: flex;
    gap: 10px;
    align-items: center;
}

.phone-input-group input {
    flex: 1;
}

.btn-select-contact {
    background: linear-gradient(135deg, #17a2b8, #138496);
    color: white;
    border: none;
    padding: 12px 15px;
    border-radius: 6px;
    cursor: pointer;
    transition: all 0.3s ease;
    min-width: 50px;
}

.btn-select-contact:hover {
    transform: translateY(-1px);
    box-shadow: 0 2px 8px rgba(23, 162, 184, 0.3);
}

.bulk-numbers-group {
    position: relative;
}

.bulk-contacts-actions {
    display: flex;
    gap: 10px;
    margin-top: 10px;
}

.btn-select-contacts,
.btn-select-all-contacts {
    background: linear-gradient(135deg, #17a2b8, #138496);
    color: white;
    border: none;
    padding: 8px 15px;
    border-radius: 6px;
    cursor: pointer;
    font-size: 0.9rem;
    transition: all 0.3s ease;
}

.btn-select-contacts:hover,
.btn-select-all-contacts:hover {
    transform: translateY(-1px);
    box-shadow: 0 2px 8px rgba(23, 162, 184, 0.3);
}

/* نافذة اختيار جهات الاتصال */
.contact-selector-modal {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0,0,0,0.5);
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 1000;
}

.contact-selector-content {
    background: white;
    border-radius: 10px;
    padding: 20px;
    max-width: 500px;
    width: 90%;
    max-height: 80vh;
    overflow-y: auto;
}

.contact-selector-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
    padding-bottom: 15px;
    border-bottom: 2px solid #e9ecef;
}

.contact-selector-header h3 {
    margin: 0;
    color: #2c3e50;
}

.btn-close-selector {
    background: none;
    border: none;
    font-size: 1.5rem;
    cursor: pointer;
    color: #6c757d;
    padding: 5px;
}

.contact-selector-item {
    display: flex;
    align-items: center;
    padding: 10px;
    border-bottom: 1px solid #e9ecef;
    cursor: pointer;
    transition: background-color 0.3s ease;
}

.contact-selector-item:hover {
    background-color: #f8f9fa;
}

.contact-selector-item input[type="checkbox"] {
    margin-left: 10px;
    transform: scale(1.2);
}

.contact-selector-actions {
    display: flex;
    gap: 10px;
    margin-top: 20px;
    justify-content: flex-end;
}

.btn-confirm-selection {
    background: linear-gradient(135deg, #28a745, #20c997);
    color: white;
    border: none;
    padding: 10px 20px;
    border-radius: 6px;
    cursor: pointer;
    font-weight: 600;
}

.btn-cancel-selection {
    background: linear-gradient(135deg, #6c757d, #5a6268);
    color: white;
    border: none;
    padding: 10px 20px;
    border-radius: 6px;
    cursor: pointer;
    font-weight: 600;
}

.form-group input:focus,
.form-group textarea:focus {
    outline: none;
    border-color: #ff6b35;
    box-shadow: 0 0 0 3px rgba(255, 107, 53, 0.1);
}

.form-group textarea {
    resize: vertical;
    min-height: 100px;
}

.send-btn {
    background: linear-gradient(45deg, #ff6b35, #f7931e);
    color: white;
    border: none;
    padding: 1rem 2rem;
    border-radius: 10px;
    font-size: 1.1rem;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    gap: 0.5rem;
    width: 100%;
    justify-content: center;
}

.send-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 5px 20px rgba(255, 107, 53, 0.3);
}

.send-btn:disabled {
    opacity: 0.6;
    cursor: not-allowed;
    transform: none;
}

/* تبويبات الإرسال */
.send-tabs {
    display: flex;
    margin-bottom: 1.5rem;
    border-radius: 10px;
    overflow: hidden;
    background: #f0f0f0;
}

.tab-btn {
    flex: 1;
    padding: 0.8rem 1rem;
    border: none;
    background: transparent;
    cursor: pointer;
    transition: all 0.3s ease;
    font-weight: 600;
    color: #666;
}

.tab-btn.active {
    background: linear-gradient(45deg, #ff6b35, #f7931e);
    color: white;
}

.tab-content {
    display: none;
}

.tab-content.active {
    display: block;
}

/* تحسين أزرار المرفقات */
.attachment-btn {
    background: #f8f9fa;
    border: 2px dashed #ddd;
    border-radius: 10px;
    padding: 1rem;
    text-align: center;
    cursor: pointer;
    transition: all 0.3s ease;
    margin-bottom: 1rem;
}

.attachment-btn:hover {
    border-color: #ff6b35;
    background: rgba(255, 107, 53, 0.05);
}

.attachment-btn i {
    font-size: 2rem;
    color: #ff6b35;
    margin-bottom: 0.5rem;
    display: block;
}

/* قائمة المرفقات */
.attachments-list {
    margin-top: 1rem;
}

.attachment-item {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 0.5rem;
    background: #f8f9fa;
    border-radius: 8px;
    margin-bottom: 0.5rem;
}

.attachment-info {
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.attachment-remove {
    background: #dc3545;
    color: white;
    border: none;
    border-radius: 50%;
    width: 24px;
    height: 24px;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
}

/* Footer */
.footer {
    background: rgba(0, 0, 0, 0.95);
    backdrop-filter: blur(20px);
    border-top: 1px solid rgba(255, 255, 255, 0.1);
    padding: 2rem 0;
    margin-top: 2rem;
    transition: all 0.3s ease;
}

/* الفوتر مرئي دائماً */

.footer-content {
    display: flex;
    justify-content: center;
    align-items: center;
    flex-direction: column;
    text-align: center;
    gap: 1rem;
}

.footer-info p {
    color: rgba(255, 255, 255, 0.8);
    margin-bottom: 0.5rem;
    font-size: 0.95rem;
}

.footer-info p:first-child {
    color: #ffffff;
    font-weight: 600;
}

.footer-logo {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    color: #ff6b35;
    font-weight: 700;
    font-size: 1.2rem;
}

.footer-logo i {
    font-size: 1.5rem;
    text-shadow: 0 0 10px rgba(255, 107, 53, 0.5);
}

/* استجابة للشاشات الصغيرة */
@media (max-width: 1024px) {
    .chat-container {
        grid-template-columns: 1fr;
        grid-template-areas:
            "header"
            "form"
            "messages";
        max-width: 100%;
    }

    .messages-area {
        border-right: none;
        border-bottom: 1px solid #e0e0e0;
        max-height: 300px;
    }

    .send-form {
        border-left: none;
        border-top: 1px solid rgba(0, 0, 0, 0.1);
    }
}

@media (max-width: 768px) {
    .chat-section {
        padding: 1rem;
        height: calc(100vh - 100px);
    }

    .send-tabs {
        flex-direction: column;
    }

    .tab-btn {
        border-radius: 0;
    }

    .tab-btn:first-child {
        border-radius: 10px 10px 0 0;
    }

    .tab-btn:last-child {
        border-radius: 0 0 10px 10px;
    }
}

/* Loading Overlay */
.loading-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.5);
    display: none;
    justify-content: center;
    align-items: center;
    z-index: 1000;
}

.loading-spinner {
    background: white;
    padding: 2rem;
    border-radius: 15px;
    text-align: center;
    box-shadow: 0 10px 40px rgba(0, 0, 0, 0.2);
}

.loading-spinner i {
    font-size: 2rem;
    color: #ff6b35;
    margin-bottom: 1rem;
}

/* Notification */
.notification {
    position: fixed;
    top: 20px;
    left: 50%;
    transform: translateX(-50%) translateY(-100px);
    background: white;
    padding: 1rem 2rem;
    border-radius: 10px;
    box-shadow: 0 5px 20px rgba(0, 0, 0, 0.2);
    z-index: 1001;
    transition: all 0.3s ease;
    opacity: 0;
    color: #333;
    font-weight: 600;
    min-width: 300px;
    text-align: center;
    border: 1px solid #e0e0e0;
}

.notification.show {
    transform: translateX(-50%) translateY(0);
    opacity: 1;
}

.notification-content {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 0.8rem;
}

.notification-icon {
    font-size: 1.2rem;
}

.notification-text {
    flex: 1;
    color: #333;
    font-size: 0.95rem;
}

.notification.success {
    background: linear-gradient(135deg, #d4edda, #c3e6cb);
    border-left: 4px solid #28a745;
    color: #155724;
}

.notification.success .notification-icon::before {
    content: '\f00c';
    font-family: 'Font Awesome 6 Free';
    font-weight: 900;
    color: #28a745;
}

.notification.success .notification-text {
    color: #155724;
}

.notification.error {
    background: linear-gradient(135deg, #f8d7da, #f5c6cb);
    border-left: 4px solid #dc3545;
    color: #721c24;
}

.notification.error .notification-icon::before {
    content: '\f00d';
    font-family: 'Font Awesome 6 Free';
    font-weight: 900;
    color: #dc3545;
}

.notification.error .notification-text {
    color: #721c24;
}

.notification.warning {
    background: linear-gradient(135deg, #fff3cd, #ffeaa7);
    border-left: 4px solid #ffc107;
    color: #856404;
}

.notification.warning .notification-icon::before {
    content: '\f071';
    font-family: 'Font Awesome 6 Free';
    font-weight: 900;
    color: #ffc107;
}

.notification.warning .notification-text {
    color: #856404;
}

.notification.info {
    background: linear-gradient(135deg, #d1ecf1, #bee5eb);
    border-left: 4px solid #17a2b8;
    color: #0c5460;
}

.notification.info .notification-icon::before {
    content: '\f05a';
    font-family: 'Font Awesome 6 Free';
    font-weight: 900;
    color: #17a2b8;
}

.notification.info .notification-text {
    color: #0c5460;
}

/* Responsive Design */
@media (max-width: 768px) {
    .container {
        padding: 0 15px;
    }
    
    .header-content {
        flex-direction: column;
        gap: 1rem;
    }
    
    .logo h1 {
        font-size: 1.5rem;
    }
    
    .welcome-card {
        padding: 2rem 1.5rem;
    }

    .welcome-card h2 {
        font-size: 2rem;
    }

    .features {
        grid-template-columns: repeat(2, 1fr);
    }
    
    .qr-card {
        padding: 2rem 1.5rem;
        max-width: 500px;
    }

    .qr-header h3 {
        font-size: 2rem;
    }

    .qr-placeholder {
        padding: 2rem;
    }

    .qr-placeholder i {
        font-size: 3rem;
    }

    #qrImage {
        max-width: 250px;
    }
    
    .chat-header {
        padding: 1rem;
    }
    
    .send-form {
        padding: 1.5rem;
    }
    
    .footer-content {
        flex-direction: column;
        text-align: center;
    }
}

/* Message Styles */
.message {
    margin-bottom: 1rem;
    padding: 1rem;
    border-radius: 10px;
    background: rgba(255, 107, 53, 0.1);
    border-right: 4px solid #ff6b35;
}

.message-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 0.5rem;
}

.message-from {
    font-weight: 600;
    color: #ff6b35;
}

.message-time {
    font-size: 0.8rem;
    color: #666;
}

.message-body {
    color: #333;
    line-height: 1.5;
}

/* File Upload Styles */
.file-form {
    margin-top: 2rem;
    padding-top: 2rem;
    border-top: 1px solid rgba(0, 0, 0, 0.1);
}

.file-form h4 {
    color: #333;
    margin-bottom: 1.5rem;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.file-form h4 i {
    color: #ff6b35;
}

.file-input-container {
    position: relative;
    border: 2px dashed #e0e0e0;
    border-radius: 10px;
    padding: 2rem;
    text-align: center;
    transition: all 0.3s ease;
    cursor: pointer;
}

.file-input-container:hover {
    border-color: #ff6b35;
    background: rgba(255, 107, 53, 0.05);
}

.file-input-container.dragover {
    border-color: #ff6b35;
    background: rgba(255, 107, 53, 0.1);
}

#fileInput {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    opacity: 0;
    cursor: pointer;
}

.file-input-display {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 0.5rem;
    color: #666;
}

.file-input-display i {
    font-size: 2rem;
    color: #ff6b35;
}

.file-selected {
    background: rgba(255, 107, 53, 0.1);
    border-color: #ff6b35;
    color: #ff6b35;
}

.file-send-btn {
    background: linear-gradient(45deg, #ff6b6b, #ee5a24);
}

.file-send-btn:hover {
    box-shadow: 0 5px 20px rgba(255, 107, 107, 0.3);
}

.file-message {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    padding: 0.5rem;
    background: rgba(0, 0, 0, 0.05);
    border-radius: 8px;
    margin-bottom: 0.5rem;
}

.file-message i {
    color: #ff6b6b;
    font-size: 1.2rem;
}

.file-caption {
    font-style: italic;
    color: #666;
    margin-top: 0.5rem;
}
