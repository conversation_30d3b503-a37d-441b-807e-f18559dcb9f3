{"name": "patriot-whatsapp-messenger", "version": "1.0.0", "description": "باتريوت - مراسل واتساب المتقدم والسريع", "main": "server.js", "scripts": {"start": "node server.js", "dev": "nodemon server.js", "test": "echo \"Error: no test specified\" && exit 1"}, "keywords": ["whatsapp", "messenger", "patriot", "advanced", "fast", "arabic", "واتساب", "مراسل", "باتريوت", "سريع"], "author": "Open Source Community", "license": "MIT", "dependencies": {"whatsapp-web.js": "^1.23.0", "express": "^4.18.2", "qrcode": "^1.5.3", "socket.io": "^4.7.2", "multer": "^1.4.5-lts.1", "cors": "^2.8.5", "helmet": "^7.0.0", "compression": "^1.7.4"}, "devDependencies": {"nodemon": "^3.0.1"}, "engines": {"node": ">=16.0.0"}, "repository": {"type": "git", "url": "https://github.com/opensource/whatsapp-messenger-opensource.git"}, "bugs": {"url": "https://github.com/opensource/whatsapp-messenger-opensource/issues"}, "homepage": "https://github.com/opensource/whatsapp-messenger-opensource#readme"}