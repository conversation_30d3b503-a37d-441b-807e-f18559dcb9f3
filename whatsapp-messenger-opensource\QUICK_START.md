# 🚀 دليل التشغيل السريع

## مشكلة: ملف start.bat يختفي؟

### ✅ الحلول السريعة:

#### 1. **جرب التشغيل السريع**
اضغط مرتين على: `quick-start.bat`

#### 2. **تشخيص المشكلة**
اضغط مرتين على: `diagnose.bat`

#### 3. **التشغيل اليدوي**
1. افتح Command Prompt (cmd)
2. اذهب إلى مجلد المشروع:
   ```cmd
   cd "مسار\المجلد\whatsapp-messenger-opensource"
   ```
3. شغل:
   ```cmd
   npm install
   npm start
   ```

## 🔧 الأسباب الشائعة:

### ❌ Node.js غير مثبت
**الحل**: حمل من https://nodejs.org/

### ❌ المتطلبات غير مثبتة
**الحل**: شغل `npm install`

### ❌ المنفذ مستخدم
**الحل**: أغلق التطبيق الآخر

### ❌ صلاحيات غير كافية
**الحل**: شغل كـ Administrator

## 📞 المساعدة السريعة:

1. **اقرأ**: `TROUBLESHOOTING.md`
2. **شخص**: `diagnose.bat`
3. **جرب**: `quick-start.bat`

---

**💡 نصيحة**: إذا لم ينجح start.bat، استخدم Command Prompt مباشرة!
