@echo off
chcp 65001 >nul
title تشخيص مشاكل واتساب مراسل

echo.
echo ========================================
echo      تشخيص مشاكل واتساب مراسل
echo ========================================
echo.

echo 🔍 فحص النظام...
echo.

REM فحص Node.js
echo 1️⃣ فحص Node.js:
node --version >nul 2>&1
if %errorlevel% neq 0 (
    echo    ❌ Node.js غير مثبت
    echo    📥 حمل من: https://nodejs.org/
) else (
    for /f "tokens=*" %%i in ('node --version') do echo    ✅ Node.js: %%i
)
echo.

REM فحص npm
echo 2️⃣ فحص npm:
npm --version >nul 2>&1
if %errorlevel% neq 0 (
    echo    ❌ npm غير متاح
) else (
    for /f "tokens=*" %%i in ('npm --version') do echo    ✅ npm: %%i
)
echo.

REM فحص الملفات المطلوبة
echo 3️⃣ فحص الملفات:
if exist "package.json" (
    echo    ✅ package.json موجود
) else (
    echo    ❌ package.json مفقود
)

if exist "server.js" (
    echo    ✅ server.js موجود
) else (
    echo    ❌ server.js مفقود
)

if exist "public\index.html" (
    echo    ✅ public\index.html موجود
) else (
    echo    ❌ public\index.html مفقود
)
echo.

REM فحص node_modules
echo 4️⃣ فحص المتطلبات:
if exist "node_modules" (
    echo    ✅ node_modules موجود
    
    REM فحص المكتبات المهمة
    if exist "node_modules\express" (
        echo    ✅ Express مثبت
    ) else (
        echo    ❌ Express مفقود
    )
    
    if exist "node_modules\whatsapp-web.js" (
        echo    ✅ WhatsApp Web.js مثبت
    ) else (
        echo    ❌ WhatsApp Web.js مفقود
    )
    
    if exist "node_modules\socket.io" (
        echo    ✅ Socket.IO مثبت
    ) else (
        echo    ❌ Socket.IO مفقود
    )
) else (
    echo    ❌ node_modules مفقود - يحتاج npm install
)
echo.

REM فحص المنافذ
echo 5️⃣ فحص المنافذ:
netstat -an | findstr :3001 >nul 2>&1
if %errorlevel% equ 0 (
    echo    ⚠️  المنفذ 3001 مستخدم بالفعل
    echo    💡 أغلق التطبيق الآخر أو استخدم منفذ مختلف
) else (
    echo    ✅ المنفذ 3001 متاح
)
echo.

REM فحص الذاكرة
echo 6️⃣ فحص الذاكرة:
for /f "skip=1 tokens=4" %%i in ('wmic OS get TotalVisibleMemorySize /value') do (
    if not "%%i"=="" (
        set /a TOTAL_RAM=%%i/1024
        if !TOTAL_RAM! LSS 2048 (
            echo    ⚠️  ذاكرة قليلة: !TOTAL_RAM! MB
            echo    💡 الحد الأدنى: 2048 MB
        ) else (
            echo    ✅ ذاكرة كافية: !TOTAL_RAM! MB
        )
    )
)
echo.

echo ========================================
echo              التوصيات
echo ========================================
echo.

if not exist "node_modules" (
    echo 📦 شغل الأمر: npm install
    echo.
)

netstat -an | findstr :3001 >nul 2>&1
if %errorlevel% equ 0 (
    echo 🔄 أغلق التطبيق الذي يستخدم المنفذ 3001
    echo.
)

node --version >nul 2>&1
if %errorlevel% neq 0 (
    echo 📥 ثبت Node.js من: https://nodejs.org/
    echo.
)

echo 🚀 بعد حل المشاكل، شغل start.bat مرة أخرى
echo.

pause
