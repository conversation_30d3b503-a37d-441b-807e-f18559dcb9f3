# دليل التثبيت - Installation Guide

## متطلبات النظام

### الحد الأدنى
- **نظام التشغيل**: Windows 10, macOS 10.14, Ubuntu 18.04 أو أحدث
- **Node.js**: الإصدار 16.0.0 أو أحدث
- **الذاكرة**: 2 GB RAM
- **مساحة القرص**: 500 MB مساحة فارغة
- **الإنترنت**: اتصال مستقر بالإنترنت

### الموصى به
- **Node.js**: الإصدار 18.0.0 أو أحدث
- **الذاكرة**: 4 GB RAM أو أكثر
- **مساحة القرص**: 1 GB مساحة فارغة
- **المتصفح**: Chrome, Firefox, Safari, Edge (أحدث إصدار)

## التثبيت خطوة بخطوة

### 1. تثبيت Node.js

#### Windows
1. اذهب إلى [nodejs.org](https://nodejs.org/)
2. حمل النسخة LTS
3. شغل الملف المحمل واتبع التعليمات
4. أعد تشغيل الكمبيوتر

#### macOS
```bash
# باستخدام Homebrew
brew install node

# أو حمل من الموقع الرسمي
# https://nodejs.org/
```

#### Linux (Ubuntu/Debian)
```bash
# تحديث النظام
sudo apt update

# تثبيت Node.js
sudo apt install nodejs npm

# التحقق من الإصدار
node --version
npm --version
```

### 2. تحميل المشروع

#### الطريقة الأولى: Git Clone
```bash
git clone https://github.com/your-username/whatsapp-messenger-opensource.git
cd whatsapp-messenger-opensource
```

#### الطريقة الثانية: تحميل ZIP
1. اذهب إلى صفحة المشروع على GitHub
2. اضغط على "Code" ثم "Download ZIP"
3. فك ضغط الملف
4. افتح مجلد المشروع في Terminal/Command Prompt

### 3. تثبيت المتطلبات

```bash
# تثبيت جميع المكتبات المطلوبة
npm install

# في حالة وجود مشاكل، جرب:
npm install --force

# أو استخدم yarn
yarn install
```

### 4. تشغيل التطبيق

```bash
# التشغيل العادي
npm start

# التشغيل في وضع التطوير (مع إعادة التحميل التلقائي)
npm run dev

# تشغيل على منفذ مختلف
PORT=8080 npm start
```

### 5. فتح التطبيق

1. افتح متصفحك
2. اذهب إلى: `http://localhost:3001`
3. ستظهر واجهة التطبيق

## استكشاف الأخطاء

### مشكلة: "command not found: node"
**الحل**: تأكد من تثبيت Node.js بشكل صحيح وإعادة تشغيل Terminal

### مشكلة: "EADDRINUSE: address already in use"
**الحل**: 
```bash
# تغيير المنفذ
PORT=3002 npm start

# أو إيقاف العملية التي تستخدم المنفذ
# Windows
netstat -ano | findstr :3001
taskkill /PID [رقم_العملية] /F

# macOS/Linux
lsof -ti:3001 | xargs kill -9
```

### مشكلة: "npm ERR! peer dep missing"
**الحل**:
```bash
npm install --legacy-peer-deps
```

### مشكلة: "Permission denied"
**الحل**:
```bash
# macOS/Linux
sudo npm install -g npm
sudo chown -R $(whoami) ~/.npm

# Windows: شغل Command Prompt كـ Administrator
```

### مشكلة: "Module not found"
**الحل**:
```bash
# حذف node_modules وإعادة التثبيت
rm -rf node_modules package-lock.json
npm install
```

## التحقق من التثبيت

### اختبار Node.js
```bash
node --version
# يجب أن يظهر: v16.0.0 أو أحدث

npm --version
# يجب أن يظهر: 8.0.0 أو أحدث
```

### اختبار التطبيق
1. شغل `npm start`
2. افتح `http://localhost:3001`
3. يجب أن تظهر صفحة الترحيب
4. يجب أن يظهر QR Code للربط

## التحديث

### تحديث المشروع
```bash
# إذا كنت تستخدم Git
git pull origin main

# إعادة تثبيت المتطلبات
npm install
```

### تحديث Node.js
```bash
# التحقق من الإصدار الحالي
node --version

# تحديث npm
npm install -g npm@latest

# لتحديث Node.js، حمل النسخة الجديدة من nodejs.org
```

## الإعدادات المتقدمة

### متغيرات البيئة
أنشئ ملف `.env` في مجلد المشروع:
```env
PORT=3001
NODE_ENV=production
SESSION_PATH=./session
UPLOADS_PATH=./uploads
```

### تشغيل كخدمة (Linux)
```bash
# إنشاء ملف خدمة
sudo nano /etc/systemd/system/whatsapp-messenger.service

# محتوى الملف:
[Unit]
Description=WhatsApp Messenger OpenSource
After=network.target

[Service]
Type=simple
User=your-username
WorkingDirectory=/path/to/whatsapp-messenger-opensource
ExecStart=/usr/bin/node server.js
Restart=always

[Install]
WantedBy=multi-user.target

# تفعيل الخدمة
sudo systemctl enable whatsapp-messenger
sudo systemctl start whatsapp-messenger
```

## الدعم

إذا واجهت أي مشاكل:
1. تحقق من [الأسئلة الشائعة](README.md#الأسئلة-الشائعة)
2. ابحث في [Issues](https://github.com/your-username/whatsapp-messenger-opensource/issues)
3. أنشئ Issue جديد مع تفاصيل المشكلة
4. انضم إلى مجتمع Discord للمساعدة الفورية

---

**نصيحة**: احتفظ بنسخة احتياطية من مجلد `session` لتجنب فقدان بيانات الربط.
