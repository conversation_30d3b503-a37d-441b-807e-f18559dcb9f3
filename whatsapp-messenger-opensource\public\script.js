// إعداد Socket.IO (سيتم تهيئته لاحقاً)
let socket;

// العناصر
let welcomeSection, qrSection, chatSection, qrPlaceholder, qrImage, statusDot, statusText;
const messagesArea = document.getElementById('messagesArea');
const messageForm = document.getElementById('messageForm');
const phoneNumber = document.getElementById('phoneNumber');
const messageText = document.getElementById('messageText');
const sendBtn = document.getElementById('sendBtn');
const refreshBtn = document.getElementById('refreshBtn');
const loadingOverlay = document.getElementById('loadingOverlay');
const notification = document.getElementById('notification');

// عناصر الملفات
const fileForm = document.getElementById('fileForm');
const filePhoneNumber = document.getElementById('filePhoneNumber');
const fileInput = document.getElementById('fileInput');
const fileCaption = document.getElementById('fileCaption');
const fileSendBtn = document.getElementById('fileSendBtn');
const fileInputContainer = document.querySelector('.file-input-container');
const fileInputDisplay = document.querySelector('.file-input-display');

// متغيرات عامة
let isClientReady = false;
let messages = [];
let selectedAttachments = [];

// تهيئة Socket.IO
function initializeSocket() {
    if (!socket) {
        console.log('🔌 تهيئة Socket.IO...');
        socket = io();

        socket.on('connect', () => {
            console.log('✅ متصل بالخادم');
        });

        socket.on('qr', (qr) => {
            console.log('📱 تم استلام QR Code');
            showQRCode(qr);
        });

        socket.on('ready', () => {
            console.log('✅ واتساب جاهز');
            showChatInterface();
        });

        socket.on('authenticated', () => {
            console.log('✅ تم التحقق من الهوية');
        });

        socket.on('auth_failure', (msg) => {
            console.error('❌ فشل في التحقق:', msg);
            showWelcomeScreen();
        });

        socket.on('disconnected', (reason) => {
            console.log('❌ انقطع الاتصال:', reason);
            showWelcomeScreen();
        });

        // معالجة الأخطاء
        socket.on('error', (error) => {
            console.error('❌ خطأ Socket.IO:', error);
            handleError(error);
        });

        // معالجة انقطاع الاتصال
        socket.on('disconnect', (reason) => {
            console.warn('⚠️ انقطع الاتصال:', reason);
            showNotification('انقطع الاتصال مع الخادم', 'warning');
        });

        // معالجة إعادة الاتصال
        socket.on('reconnect', () => {
            console.log('✅ تم إعادة الاتصال');
            showNotification('تم إعادة الاتصال بالخادم', 'success');
        });
    }

    // بدء فحص دوري لحالة الاتصال
    startStatusPolling();
}

// فحص دوري لحالة الاتصال
function startStatusPolling() {
    const statusInterval = setInterval(() => {
        // فقط إذا كنا في شاشة QR ولم نكن متصلين بعد
        if (qrSection && qrSection.style.display === 'block' && !isClientReady) {
            fetch('/api/status')
                .then(response => response.json())
                .then(data => {
                    if (data.isReady && !isClientReady) {
                        console.log('✅ تم اكتشاف الاتصال عبر الفحص الدوري! الانتقال لواجهة الدردشة...');
                        clearInterval(statusInterval);
                        showChatInterface();
                    }
                })
                .catch(error => {
                    console.error('❌ خطأ في فحص الحالة:', error);
                });
        } else if (isClientReady) {
            // إيقاف الفحص إذا كنا متصلين
            clearInterval(statusInterval);
        }
    }, 3000); // فحص كل 3 ثوان

    // إيقاف الفحص بعد 5 دقائق
    setTimeout(() => {
        clearInterval(statusInterval);
        console.log('⏰ انتهت مهلة فحص حالة الاتصال');
    }, 300000);
}

// تهيئة التطبيق
function initializeApp() {
    console.log('🚀 تم تحميل واتساب مراسل - النسخة المفتوحة المصدر');

    // تهيئة العناصر
    welcomeSection = document.getElementById('welcomeSection');
    qrSection = document.getElementById('qrSection');
    chatSection = document.getElementById('chatSection');
    qrPlaceholder = document.getElementById('qrPlaceholder');
    qrImage = document.getElementById('qrImage');
    statusDot = document.getElementById('statusDot');
    statusText = document.getElementById('statusText');

    console.log('🔍 تم العثور على العناصر:', {
        welcomeSection: !!welcomeSection,
        qrSection: !!qrSection,
        chatSection: !!chatSection
    });

    // إعداد الأحداث
    setupEventListeners();

    // التحقق من الحالة الأولية
    checkInitialStatus();
}

document.addEventListener('DOMContentLoaded', initializeApp);

// إعداد مستمعي الأحداث
function setupEventListeners() {
    // Socket.IO events will be initialized when needed

    // Form events
    if (messageForm) messageForm.addEventListener('submit', handleSendMessage);
    if (fileForm) fileForm.addEventListener('submit', handleSendFile);
    if (refreshBtn) refreshBtn.addEventListener('click', handleRefresh);

    // Home button
    const homeBtn = document.getElementById('homeBtn');
    if (homeBtn) homeBtn.addEventListener('click', handleHome);

    // Logout buttons (may not exist on all pages)
    const logoutBtn = document.getElementById('logoutBtn');
    const headerLogoutBtn = document.getElementById('headerLogoutBtn');
    if (logoutBtn) logoutBtn.addEventListener('click', handleLogout);
    if (headerLogoutBtn) headerLogoutBtn.addEventListener('click', handleLogout);

    // Start button event
    const startBtn = document.getElementById('startBtn');
    console.log('🔍 البحث عن زر البدء:', startBtn);
    if (startBtn) {
        console.log('✅ تم العثور على زر البدء، إضافة مستمع الأحداث');
        startBtn.addEventListener('click', handleStart);
    } else {
        console.error('❌ لم يتم العثور على زر البدء');
    }

    // File input events
    if (fileInput) {
        fileInput.addEventListener('change', handleFileSelect);
    }
    if (fileInputContainer) {
        fileInputContainer.addEventListener('dragover', handleDragOver);
        fileInputContainer.addEventListener('dragleave', handleDragLeave);
        fileInputContainer.addEventListener('drop', handleFileDrop);
    }
}

// التحقق من الحالة الأولية
async function checkInitialStatus() {
    try {
        const response = await fetch('/api/status');
        const status = await response.json();
        
        if (status.isReady) {
            showChatInterface();
        } else if (status.hasQR) {
            const qrResponse = await fetch('/api/qr');
            const qrData = await qrResponse.json();
            if (qrData.qr) {
                showQRCode(qrData.qr);
            } else {
                showWelcomeScreen();
            }
        } else {
            showWelcomeScreen();
        }
    } catch (error) {
        console.error('خطأ في التحقق من الحالة:', error);
        showWelcomeScreen();
    }
}

// تحديث حالة الاتصال
function updateStatus(status, text) {
    statusDot.className = `status-dot ${status}`;
    statusText.textContent = text;
}

// إعادة تعيين حالة التهيئة
async function resetInitialization() {
    try {
        const response = await fetch('/api/reset-init', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            }
        });
        const data = await response.json();
        console.log('🔄 إعادة تعيين التهيئة:', data);
        return data.success;
    } catch (error) {
        console.error('❌ خطأ في إعادة تعيين التهيئة:', error);
        return false;
    }
}

// معالجة زر البدء - حل مبسط
function handleStart() {
    console.log('🚀 تم الضغط على زر ابدأ الآن');

    // إظهار رسالة تحميل
    const startBtn = document.getElementById('startBtn');
    if (startBtn) {
        startBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> جاري التحميل...';
        startBtn.disabled = true;
    }

    // إرسال طلب التهيئة
    fetch('/api/initialize', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json'
        }
    }).then(response => {
        console.log('📡 استجابة الخادم:', response.status);
        return response.json();
    })
    .then(data => {
        console.log('📡 بيانات الاستجابة:', data);
        if (data.success) {
            console.log('✅ تم بدء عملية التهيئة بنجاح');

            // إخفاء شاشة الترحيب وإظهار QR
            if (welcomeSection) {
                welcomeSection.style.display = 'none';
            }
            if (qrSection) {
                qrSection.style.display = 'block';
            }

            // بدء استطلاع QR Code
            pollForQRCode();

        } else {
            console.error('❌ فشل في التهيئة:', data.message);
            if (startBtn) {
                startBtn.innerHTML = '<i class="fas fa-exclamation-triangle"></i> فشل في التهيئة';
                startBtn.disabled = false;
            }
        }
    }).catch(error => {
        console.error('❌ خطأ في التهيئة:', error);
        if (startBtn) {
            startBtn.innerHTML = '<i class="fas fa-exclamation-triangle"></i> خطأ في الاتصال';
            startBtn.disabled = false;
        }
    });
}

// استطلاع QR Code من الخادم
function pollForQRCode() {
    const qrPlaceholder = document.getElementById('qrPlaceholder');
    const qrImage = document.getElementById('qrImage');

    if (qrPlaceholder) {
        qrPlaceholder.style.display = 'block';
        qrPlaceholder.innerHTML = '<i class="fas fa-spinner fa-spin"></i><span>جاري إنشاء QR Code...</span>';
    }
    if (qrImage) {
        qrImage.style.display = 'none';
    }

    let attempts = 0;
    const maxAttempts = 30; // 30 ثانية

    const checkQR = () => {
        attempts++;
        console.log(`🔍 محاولة ${attempts} للحصول على QR Code`);

        fetch('/api/qr')
        .then(response => response.json())
        .then(data => {
            if (data.success && data.qr) {
                console.log('✅ تم الحصول على QR Code');
                if (qrPlaceholder) qrPlaceholder.style.display = 'none';
                if (qrImage) {
                    qrImage.src = data.qr;
                    qrImage.style.display = 'block';
                }

                // تهيئة Socket.IO للاستماع لحالة الاتصال
                initializeSocket();

            } else if (attempts < maxAttempts) {
                // إعادة المحاولة بعد ثانية واحدة
                setTimeout(checkQR, 1000);
            } else {
                console.error('❌ انتهت المحاولات للحصول على QR Code');
                if (qrPlaceholder) {
                    qrPlaceholder.innerHTML = '<i class="fas fa-exclamation-triangle"></i><span>فشل في إنشاء QR Code</span>';
                }
            }
        })
        .catch(error => {
            console.error('❌ خطأ في الحصول على QR Code:', error);
            if (attempts < maxAttempts) {
                setTimeout(checkQR, 1000);
            } else {
                if (qrPlaceholder) {
                    qrPlaceholder.innerHTML = '<i class="fas fa-exclamation-triangle"></i><span>خطأ في الاتصال</span>';
                }
            }
        });
    };

    // بدء الاستطلاع
    checkQR();
}

// عرض شاشة الترحيب
function showWelcomeScreen() {
    welcomeSection.style.display = 'block';
    qrSection.style.display = 'none';
    chatSection.style.display = 'none';

    // إخفاء أزرار الهيدر
    const headerActions = document.querySelector('.header-actions');
    if (headerActions) {
        headerActions.classList.remove('show');
    }

    updateStatus('disconnected', 'غير متصل');
    isClientReady = false;
}

// عرض QR Code
function showQRCode(qrData) {
    welcomeSection.style.display = 'none';
    qrSection.style.display = 'block';
    chatSection.style.display = 'none';
    
    qrPlaceholder.style.display = 'none';
    qrImage.src = qrData;
    qrImage.style.display = 'block';
    
    updateStatus('connecting', 'في انتظار المسح...');
    isClientReady = false;
}

// عرض واجهة الدردشة
function showChatInterface() {
    welcomeSection.style.display = 'none';
    qrSection.style.display = 'none';
    chatSection.style.display = 'block';

    // إظهار أزرار الهيدر
    const headerActions = document.querySelector('.header-actions');
    if (headerActions) {
        headerActions.classList.add('show');
    }

    updateStatus('connected', 'متصل');
    isClientReady = true;

    showNotification('تم الاتصال بنجاح!', 'success');
}

// إضافة رسالة إلى المحادثة
function addMessage(messageData) {
    const messageElement = document.createElement('div');
    messageElement.className = 'message';
    
    const messageTime = new Date(messageData.timestamp * 1000).toLocaleString('ar-SA');
    
    messageElement.innerHTML = `
        <div class="message-header">
            <span class="message-from">${formatPhoneNumber(messageData.from)}</span>
            <span class="message-time">${messageTime}</span>
        </div>
        <div class="message-body">${escapeHtml(messageData.body)}</div>
    `;
    
    // إزالة رسالة الترحيب إذا كانت موجودة
    const welcomeMessage = messagesArea.querySelector('.welcome-message');
    if (welcomeMessage) {
        welcomeMessage.remove();
    }
    
    messagesArea.appendChild(messageElement);
    messagesArea.scrollTop = messagesArea.scrollHeight;
    
    messages.push(messageData);
}

// معالجة إرسال الرسالة
async function handleSendMessage(event) {
    event.preventDefault();
    
    if (!isClientReady) {
        showNotification('WhatsApp غير متصل، يرجى الانتظار', 'error');
        return;
    }
    
    const phone = phoneNumber.value.trim();
    const message = messageText.value.trim();
    
    if (!phone || !message) {
        showNotification('يرجى إدخال رقم الهاتف والرسالة', 'error');
        return;
    }
    
    // تعطيل الزر أثناء الإرسال
    sendBtn.disabled = true;
    sendBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> جاري الإرسال...';
    
    try {
        // إرسال الرسالة النصية أولاً
        const response = await fetch('/api/send-message', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({
                number: phone,
                message: message
            })
        });

        const result = await response.json();

        if (result.success) {
            showNotification('تم إرسال الرسالة بنجاح!', 'success');

            // إرسال المرفقات إذا كانت موجودة
            if (selectedAttachments.length > 0) {
                await sendAttachments(phone);
            }

            messageText.value = '';
            selectedAttachments = [];
            updateAttachmentsList();

            // إضافة الرسالة المرسلة إلى المحادثة
            addSentMessage(phone, message);
        } else {
            showNotification(`فشل في إرسال الرسالة: ${result.message}`, 'error');
        }
    } catch (error) {
        console.error('خطأ في إرسال الرسالة:', error);
        showNotification('حدث خطأ في إرسال الرسالة', 'error');
    } finally {
        // إعادة تفعيل الزر
        sendBtn.disabled = false;
        sendBtn.innerHTML = '<i class="fas fa-paper-plane"></i> إرسال الرسالة';
    }
}

// إضافة رسالة مرسلة
function addSentMessage(phone, message) {
    const messageElement = document.createElement('div');
    messageElement.className = 'message sent-message';
    messageElement.style.background = 'rgba(255, 107, 53, 0.2)';
    messageElement.style.borderRight = '4px solid #ff6b35';
    messageElement.style.marginLeft = '2rem';
    
    const messageTime = new Date().toLocaleString('ar-SA');
    
    messageElement.innerHTML = `
        <div class="message-header">
            <span class="message-from">أنت → ${formatPhoneNumber(phone)}</span>
            <span class="message-time">${messageTime}</span>
        </div>
        <div class="message-body">${escapeHtml(message)}</div>
    `;
    
    // إزالة رسالة الترحيب إذا كانت موجودة
    const welcomeMessage = messagesArea.querySelector('.welcome-message');
    if (welcomeMessage) {
        welcomeMessage.remove();
    }
    
    messagesArea.appendChild(messageElement);
    messagesArea.scrollTop = messagesArea.scrollHeight;
}

// معالجة إعادة التحميل
async function handleRefresh() {
    showLoading(true);
    
    try {
        const response = await fetch('/api/restart', {
            method: 'POST'
        });
        
        const result = await response.json();
        
        if (result.success) {
            showNotification('جاري إعادة تشغيل الاتصال...', 'info');
            setTimeout(() => {
                location.reload();
            }, 2000);
        } else {
            showNotification('فشل في إعادة التشغيل', 'error');
        }
    } catch (error) {
        console.error('خطأ في إعادة التشغيل:', error);
        showNotification('حدث خطأ في إعادة التشغيل', 'error');
    } finally {
        showLoading(false);
    }
}

// معالجة العودة للرئيسية
function handleHome() {
    if (confirm('هل تريد العودة إلى الصفحة الرئيسية؟ سيبقى الواتساب متصلاً.')) {
        // إخفاء جميع الأقسام
        if (welcomeSection) welcomeSection.style.display = 'block';
        if (qrSection) qrSection.style.display = 'none';
        if (chatSection) chatSection.style.display = 'none';

        // إخفاء أزرار الهيدر
        const headerActions = document.querySelector('.header-actions');
        if (headerActions) {
            headerActions.style.display = 'none';
        }

        showNotification('تم العودة للصفحة الرئيسية', 'success');
    }
}

// معالجة تسجيل الخروج
async function handleLogout() {
    if (!confirm('هل أنت متأكد من تسجيل الخروج؟\n\nسيتم:\n• قطع الاتصال من الجوال\n• حذف جلسة الاتصال\n• العودة لشاشة البداية')) {
        return;
    }

    showLoading(true);
    showNotification('جاري تسجيل الخروج من واتساب...', 'info');

    try {
        const response = await fetch('/api/logout', {
            method: 'POST'
        });

        const result = await response.json();

        if (result.success) {
            showNotification('تم تسجيل الخروج بنجاح من الجوال والموقع', 'success');

            // إعادة تعيين المتغيرات
            isClientReady = false;

            // إيقاف Socket.IO
            if (socket) {
                socket.disconnect();
                socket = null;
            }

            setTimeout(() => {
                location.reload();
            }, 2000);
        } else {
            showNotification('فشل في تسجيل الخروج', 'error');
        }
    } catch (error) {
        console.error('خطأ في تسجيل الخروج:', error);
        showNotification('حدث خطأ في تسجيل الخروج', 'error');
    } finally {
        showLoading(false);
    }
}

// عرض/إخفاء شاشة التحميل
function showLoading(show) {
    loadingOverlay.style.display = show ? 'flex' : 'none';
}



// تنسيق رقم الهاتف
function formatPhoneNumber(phone) {
    return phone.replace('@c.us', '').replace(/(\d{3})(\d{3})(\d{4})/, '$1 $2 $3');
}

// معالجة اختيار الملف
function handleFileSelect(event) {
    const file = event.target.files[0];
    if (file) {
        updateFileDisplay(file);
    }
}

// معالجة السحب والإفلات
function handleDragOver(event) {
    event.preventDefault();
    if (fileInputContainer) {
        fileInputContainer.classList.add('dragover');
    }
}

function handleDragLeave(event) {
    event.preventDefault();
    if (fileInputContainer) {
        fileInputContainer.classList.remove('dragover');
    }
}

function handleFileDrop(event) {
    event.preventDefault();
    if (fileInputContainer) {
        fileInputContainer.classList.remove('dragover');
    }

    const files = event.dataTransfer.files;
    if (files.length > 0 && fileInput) {
        fileInput.files = files;
        updateFileDisplay(files[0]);
    }
}

// تحديث عرض الملف المختار
function updateFileDisplay(file) {
    if (fileInputContainer) {
        fileInputContainer.classList.add('file-selected');
    }
    if (fileInputDisplay) {
        fileInputDisplay.innerHTML = `
            <i class="fas fa-file"></i>
            <span>${file.name}</span>
            <small>${formatFileSize(file.size)}</small>
        `;
    }
}

// معالجة إرسال الملف
async function handleSendFile(event) {
    event.preventDefault();

    if (!isClientReady) {
        showNotification('WhatsApp غير متصل، يرجى الانتظار', 'error');
        return;
    }

    const phone = filePhoneNumber.value.trim();
    const file = fileInput.files[0];
    const caption = fileCaption.value.trim();

    if (!phone || !file) {
        showNotification('يرجى إدخال رقم الهاتف واختيار ملف', 'error');
        return;
    }

    // تعطيل الزر أثناء الإرسال
    fileSendBtn.disabled = true;
    fileSendBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> جاري الإرسال...';

    try {
        const formData = new FormData();
        formData.append('file', file);
        formData.append('number', phone);
        if (caption) {
            formData.append('caption', caption);
        }

        const response = await fetch('/api/send-file', {
            method: 'POST',
            body: formData
        });

        const result = await response.json();

        if (result.success) {
            showNotification('تم إرسال الملف بنجاح!', 'success');

            // إعادة تعيين النموذج
            if (fileForm) {
                fileForm.reset();
            }
            if (fileInputContainer) {
                fileInputContainer.classList.remove('file-selected');
            }
            if (fileInputDisplay) {
                fileInputDisplay.innerHTML = `
                    <i class="fas fa-cloud-upload-alt"></i>
                    <span>اضغط لاختيار ملف أو اسحبه هنا</span>
                `;
            }

            // إضافة الملف المرسل إلى المحادثة
            addSentFile(phone, file.name, caption);
        } else {
            showNotification(`فشل في إرسال الملف: ${result.message}`, 'error');
        }
    } catch (error) {
        console.error('خطأ في إرسال الملف:', error);
        showNotification('حدث خطأ في إرسال الملف', 'error');
    } finally {
        // إعادة تفعيل الزر
        fileSendBtn.disabled = false;
        fileSendBtn.innerHTML = '<i class="fas fa-upload"></i> إرسال الملف';
    }
}

// إضافة ملف مرسل
function addSentFile(phone, fileName, caption) {
    const messageElement = document.createElement('div');
    messageElement.className = 'message sent-message';
    messageElement.style.background = 'rgba(255, 107, 107, 0.2)';
    messageElement.style.borderRight = '4px solid #ff6b6b';
    messageElement.style.marginLeft = '2rem';

    const messageTime = new Date().toLocaleString('ar-SA');

    messageElement.innerHTML = `
        <div class="message-header">
            <span class="message-from">أنت → ${formatPhoneNumber(phone)}</span>
            <span class="message-time">${messageTime}</span>
        </div>
        <div class="message-body">
            <div class="file-message">
                <i class="fas fa-file"></i>
                <span>${fileName}</span>
            </div>
            ${caption ? `<div class="file-caption">${escapeHtml(caption)}</div>` : ''}
        </div>
    `;

    // إزالة رسالة الترحيب إذا كانت موجودة
    const welcomeMessage = messagesArea.querySelector('.welcome-message');
    if (welcomeMessage) {
        welcomeMessage.remove();
    }

    messagesArea.appendChild(messageElement);
    messagesArea.scrollTop = messagesArea.scrollHeight;
}

// تنسيق حجم الملف
function formatFileSize(bytes) {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
}

// تأمين النص من HTML
function escapeHtml(text) {
    const div = document.createElement('div');
    div.textContent = text;
    return div.innerHTML;
}







// معالجة المرفقات المتعددة
document.addEventListener('DOMContentLoaded', function() {
    const attachmentsInput = document.getElementById('attachments');
    const attachmentsList = document.getElementById('attachmentsList');

    if (attachmentsInput) {
        attachmentsInput.addEventListener('change', handleAttachmentsChange);
    }
});

function handleAttachmentsChange(event) {
    const files = Array.from(event.target.files);

    files.forEach(file => {
        if (!selectedAttachments.find(att => att.name === file.name && att.size === file.size)) {
            selectedAttachments.push(file);
        }
    });

    updateAttachmentsList();
}

function updateAttachmentsList() {
    const attachmentsList = document.getElementById('attachmentsList');
    const attachmentCount = document.getElementById('attachmentCount');

    if (!attachmentsList) return;

    attachmentsList.innerHTML = '';

    // تحديث عدد المرفقات
    if (attachmentCount) {
        if (selectedAttachments.length > 0) {
            attachmentCount.textContent = `(${selectedAttachments.length} مرفق)`;
        } else {
            attachmentCount.textContent = '';
        }
    }

    selectedAttachments.forEach((file, index) => {
        const attachmentItem = document.createElement('div');
        attachmentItem.className = 'attachment-item';

        const fileName = document.createElement('span');
        fileName.textContent = file.name;

        const removeBtn = document.createElement('button');
        removeBtn.className = 'remove-attachment';
        removeBtn.innerHTML = '×';
        removeBtn.onclick = () => removeAttachment(index);

        attachmentItem.appendChild(fileName);
        attachmentItem.appendChild(removeBtn);
        attachmentsList.appendChild(attachmentItem);
    });
}

function removeAttachment(index) {
    selectedAttachments.splice(index, 1);
    updateAttachmentsList();
}

// إرسال المرفقات
async function sendAttachments(phone) {
    const sendDelay = parseInt(document.getElementById('sendDelay')?.value || 3) * 1000;

    for (let i = 0; i < selectedAttachments.length; i++) {
        const file = selectedAttachments[i];

        try {
            const formData = new FormData();
            formData.append('file', file);
            formData.append('number', phone);
            formData.append('caption', `مرفق ${i + 1}: ${file.name}`);

            const response = await fetch('/api/send-file', {
                method: 'POST',
                body: formData
            });

            const result = await response.json();

            if (result.success) {
                showNotification(`تم إرسال المرفق: ${file.name}`, 'success');
            } else {
                showNotification(`فشل في إرسال المرفق: ${file.name}`, 'error');
            }

            // تأخير بين المرفقات
            if (i < selectedAttachments.length - 1) {
                await new Promise(resolve => setTimeout(resolve, sendDelay));
            }

        } catch (error) {
            console.error('خطأ في إرسال المرفق:', error);
            showNotification(`خطأ في إرسال المرفق: ${file.name}`, 'error');
        }
    }
}

// معالجة الأخطاء العامة
function handleError(error) {
    console.error('خطأ:', error);
    showNotification('حدث خطأ: ' + (error.message || error), 'error');

    // إخفاء شاشة التحميل في حالة الخطأ
    const loadingOverlay = document.getElementById('loadingOverlay');
    if (loadingOverlay) {
        loadingOverlay.style.display = 'none';
    }
}

// إعداد التبويبات
function setupTabs() {
    const tabButtons = document.querySelectorAll('.tab-btn');
    const tabContents = document.querySelectorAll('.tab-content');

    tabButtons.forEach(button => {
        button.addEventListener('click', () => {
            const targetTab = button.getAttribute('data-tab');

            // إزالة الفئة النشطة من جميع الأزرار والمحتويات
            tabButtons.forEach(btn => btn.classList.remove('active'));
            tabContents.forEach(content => content.classList.remove('active'));

            // إضافة الفئة النشطة للزر المحدد
            button.classList.add('active');

            // إظهار المحتوى المناسب
            const targetContent = document.getElementById(targetTab + 'Tab');
            if (targetContent) {
                targetContent.classList.add('active');
            }
        });
    });
}

// إعداد نموذج الرسائل الجماعية
function setupBulkMessageForm() {
    const bulkForm = document.getElementById('bulkMessageForm');
    if (bulkForm) {
        bulkForm.addEventListener('submit', handleBulkMessage);
    }
}

// معالجة إرسال الرسائل الجماعية
async function handleBulkMessage(e) {
    e.preventDefault();

    const numbers = document.getElementById('bulkNumbers').value.trim();
    const message = document.getElementById('bulkMessage').value.trim();
    const delay = parseInt(document.getElementById('bulkDelay').value) || 5;
    const attachments = document.getElementById('bulkAttachments').files;

    if (!numbers || !message) {
        showNotification('يرجى إدخال الأرقام والرسالة', 'error');
        return;
    }

    const phoneNumbers = numbers.split('\n').filter(num => num.trim());
    if (phoneNumbers.length === 0) {
        showNotification('يرجى إدخال أرقام هواتف صحيحة', 'error');
        return;
    }

    const bulkSendBtn = document.getElementById('bulkSendBtn');
    bulkSendBtn.disabled = true;
    bulkSendBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> جاري الإرسال...';

    let successCount = 0;
    let errorCount = 0;

    for (let i = 0; i < phoneNumbers.length; i++) {
        const phoneNumber = phoneNumbers[i].trim();

        try {
            // إرسال الرسالة النصية
            const response = await fetch('/api/send-message', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    number: phoneNumber,
                    message: message
                })
            });

            const result = await response.json();

            if (result.success) {
                successCount++;
                showNotification(`تم إرسال الرسالة ${i + 1} من ${phoneNumbers.length}`, 'success');

                // إرسال المرفقات إن وجدت
                if (attachments.length > 0) {
                    for (let j = 0; j < attachments.length; j++) {
                        const file = attachments[j];
                        const formData = new FormData();
                        formData.append('file', file);
                        formData.append('number', phoneNumber);
                        formData.append('caption', `مرفق ${j + 1}: ${file.name}`);

                        const fileResponse = await fetch('/api/send-file', {
                            method: 'POST',
                            body: formData
                        });

                        const fileResult = await fileResponse.json();
                        if (!fileResult.success) {
                            console.error(`فشل في إرسال المرفق ${file.name} إلى ${phoneNumber}`);
                        }
                    }
                }
            } else {
                throw new Error(result.error || 'فشل في الإرسال');
            }
        } catch (error) {
            errorCount++;
            console.error(`فشل في إرسال الرسالة إلى ${phoneNumber}:`, error);
        }

        // تأخير بين الرسائل (إلا للرسالة الأخيرة)
        if (i < phoneNumbers.length - 1) {
            await new Promise(resolve => setTimeout(resolve, delay * 1000));
        }
    }

    // إعادة تعيين الزر
    bulkSendBtn.disabled = false;
    bulkSendBtn.innerHTML = '<i class="fas fa-bullhorn"></i> إرسال للجميع';

    // إظهار النتائج النهائية
    if (successCount > 0) {
        showNotification(`تم إرسال ${successCount} رسالة بنجاح`, 'success');
    }
    if (errorCount > 0) {
        showNotification(`فشل في إرسال ${errorCount} رسالة`, 'error');
    }
}

// تحسين معالجة المرفقات
function setupAttachments() {
    const attachmentsInput = document.getElementById('attachments');
    const attachmentsList = document.getElementById('attachmentsList');
    const selectedFileInfo = document.getElementById('selectedFileInfo');
    const selectedFileName = document.getElementById('selectedFileName');
    const bulkAttachmentsInput = document.getElementById('bulkAttachments');
    const bulkAttachmentsList = document.getElementById('bulkAttachmentsList');

    if (attachmentsInput) {
        attachmentsInput.addEventListener('change', handleAttachmentsChange);
    }

    if (fileInput) {
        fileInput.addEventListener('change', handleFileInputChange);
    }

    if (bulkAttachmentsInput) {
        bulkAttachmentsInput.addEventListener('change', handleBulkAttachmentsChange);
    }
}

function handleAttachmentsChange(e) {
    const files = Array.from(e.target.files);
    const attachmentsList = document.getElementById('attachmentsList');

    attachmentsList.innerHTML = '';

    files.forEach((file, index) => {
        const attachmentItem = document.createElement('div');
        attachmentItem.className = 'attachment-item';
        attachmentItem.innerHTML = `
            <div class="attachment-info">
                <i class="fas fa-file"></i>
                <span>${file.name}</span>
            </div>
            <button type="button" class="attachment-remove" onclick="removeAttachment(${index})">
                <i class="fas fa-times"></i>
            </button>
        `;
        attachmentsList.appendChild(attachmentItem);
    });
}

function handleFileInputChange(e) {
    const file = e.target.files[0];
    const selectedFileInfo = document.getElementById('selectedFileInfo');
    const selectedFileName = document.getElementById('selectedFileName');

    if (file) {
        selectedFileName.textContent = file.name;
        selectedFileInfo.style.display = 'block';
    } else {
        selectedFileInfo.style.display = 'none';
    }
}

function removeAttachment(index) {
    const attachmentsInput = document.getElementById('attachments');
    const dt = new DataTransfer();
    const files = Array.from(attachmentsInput.files);

    files.forEach((file, i) => {
        if (i !== index) {
            dt.items.add(file);
        }
    });

    attachmentsInput.files = dt.files;
    handleAttachmentsChange({ target: attachmentsInput });
}

function clearSelectedFile() {
    const selectedFileInfo = document.getElementById('selectedFileInfo');

    if (fileInput) {
        fileInput.value = '';
    }
    if (selectedFileInfo) {
        selectedFileInfo.style.display = 'none';
    }
}

function handleBulkAttachmentsChange(e) {
    const files = Array.from(e.target.files);
    const bulkAttachmentsList = document.getElementById('bulkAttachmentsList');

    bulkAttachmentsList.innerHTML = '';

    files.forEach((file, index) => {
        const attachmentItem = document.createElement('div');
        attachmentItem.className = 'attachment-item';
        attachmentItem.innerHTML = `
            <div class="attachment-info">
                <i class="fas fa-file"></i>
                <span>${file.name}</span>
            </div>
            <button type="button" class="attachment-remove" onclick="removeBulkAttachment(${index})">
                <i class="fas fa-times"></i>
            </button>
        `;
        bulkAttachmentsList.appendChild(attachmentItem);
    });
}

function removeBulkAttachment(index) {
    const bulkAttachmentsInput = document.getElementById('bulkAttachments');
    const dt = new DataTransfer();
    const files = Array.from(bulkAttachmentsInput.files);

    files.forEach((file, i) => {
        if (i !== index) {
            dt.items.add(file);
        }
    });

    bulkAttachmentsInput.files = dt.files;
    handleBulkAttachmentsChange({ target: bulkAttachmentsInput });
}

function handleBulkAttachmentsChange(e) {
    const files = Array.from(e.target.files);
    const bulkAttachmentsList = document.getElementById('bulkAttachmentsList');

    bulkAttachmentsList.innerHTML = '';

    files.forEach((file, index) => {
        const attachmentItem = document.createElement('div');
        attachmentItem.className = 'attachment-item';
        attachmentItem.innerHTML = `
            <div class="attachment-info">
                <i class="fas fa-file"></i>
                <span>${file.name}</span>
            </div>
            <button type="button" class="attachment-remove" onclick="removeBulkAttachment(${index})">
                <i class="fas fa-times"></i>
            </button>
        `;
        bulkAttachmentsList.appendChild(attachmentItem);
    });
}

function removeBulkAttachment(index) {
    const bulkAttachmentsInput = document.getElementById('bulkAttachments');
    const dt = new DataTransfer();
    const files = Array.from(bulkAttachmentsInput.files);

    files.forEach((file, i) => {
        if (i !== index) {
            dt.items.add(file);
        }
    });

    bulkAttachmentsInput.files = dt.files;
    handleBulkAttachmentsChange({ target: bulkAttachmentsInput });
}

// عرض الإشعارات المحسنة
function showNotification(message, type = 'info') {
    // إزالة الإشعارات السابقة
    const existingNotifications = document.querySelectorAll('.notification');
    existingNotifications.forEach(notification => notification.remove());

    const notification = document.createElement('div');
    notification.className = `notification ${type}`;
    notification.innerHTML = `
        <div class="notification-content">
            <div class="notification-icon"></div>
            <div class="notification-text">${message}</div>
        </div>
    `;

    document.body.appendChild(notification);

    // إظهار الإشعار
    setTimeout(() => {
        notification.classList.add('show');
    }, 100);

    // إخفاء الإشعار بعد 4 ثوانٍ
    setTimeout(() => {
        notification.classList.remove('show');
        setTimeout(() => {
            if (notification.parentNode) {
                notification.remove();
            }
        }, 300);
    }, 4000);
}

// ===== إدارة جهات الاتصال =====

// متغيرات جهات الاتصال
let contacts = [];
let editingContactId = null;

// تحميل جهات الاتصال من التخزين المحلي
function loadContacts() {
    try {
        const savedContacts = localStorage.getItem('whatsapp_contacts');
        if (savedContacts) {
            contacts = JSON.parse(savedContacts);
        }
    } catch (error) {
        console.error('خطأ في تحميل جهات الاتصال:', error);
        contacts = [];
    }
    renderContactsList();
}

// حفظ جهات الاتصال في التخزين المحلي
function saveContacts() {
    try {
        localStorage.setItem('whatsapp_contacts', JSON.stringify(contacts));
    } catch (error) {
        console.error('خطأ في حفظ جهات الاتصال:', error);
        showNotification('فشل في حفظ جهات الاتصال', 'error');
    }
}

// إضافة جهة اتصال جديدة
function addContact(name, phone, notes = '') {
    const id = Date.now().toString();
    const contact = {
        id,
        name: name.trim(),
        phone: phone.trim(),
        notes: notes.trim(),
        createdAt: new Date().toISOString()
    };

    // التحقق من عدم تكرار الرقم
    const existingContact = contacts.find(c => c.phone === contact.phone);
    if (existingContact) {
        showNotification('هذا الرقم موجود بالفعل', 'error');
        return false;
    }

    contacts.push(contact);
    saveContacts();
    renderContactsList();
    showNotification('تم إضافة جهة الاتصال بنجاح', 'success');
    return true;
}

// تحديث جهة اتصال
function updateContact(id, name, phone, notes = '') {
    const contactIndex = contacts.findIndex(c => c.id === id);
    if (contactIndex === -1) {
        showNotification('جهة الاتصال غير موجودة', 'error');
        return false;
    }

    // التحقق من عدم تكرار الرقم (إلا إذا كان نفس الرقم)
    const existingContact = contacts.find(c => c.phone === phone.trim() && c.id !== id);
    if (existingContact) {
        showNotification('هذا الرقم موجود بالفعل', 'error');
        return false;
    }

    contacts[contactIndex] = {
        ...contacts[contactIndex],
        name: name.trim(),
        phone: phone.trim(),
        notes: notes.trim(),
        updatedAt: new Date().toISOString()
    };

    saveContacts();
    renderContactsList();
    showNotification('تم تحديث جهة الاتصال بنجاح', 'success');
    return true;
}

// حذف جهة اتصال
function deleteContact(id) {
    const contactIndex = contacts.findIndex(c => c.id === id);
    if (contactIndex === -1) {
        showNotification('جهة الاتصال غير موجودة', 'error');
        return;
    }

    const contact = contacts[contactIndex];
    if (confirm(`هل أنت متأكد من حذف جهة الاتصال "${contact.name}"؟`)) {
        contacts.splice(contactIndex, 1);
        saveContacts();
        renderContactsList();
        showNotification('تم حذف جهة الاتصال بنجاح', 'success');
    }
}

// عرض قائمة جهات الاتصال
function renderContactsList() {
    const contactsList = document.getElementById('contactsList');
    const contactsEmpty = document.getElementById('contactsEmpty');

    if (!contactsList) return;

    if (contacts.length === 0) {
        contactsList.innerHTML = `
            <div class="contacts-empty" id="contactsEmpty">
                <i class="fas fa-address-book"></i>
                <p>لا توجد جهات اتصال محفوظة</p>
                <p>اضغط على "إضافة جهة اتصال" لبدء إضافة الأرقام</p>
            </div>
        `;
        return;
    }

    const contactsHTML = contacts.map(contact => `
        <div class="contact-item" data-id="${contact.id}">
            <div class="contact-info">
                <div class="contact-name">${contact.name}</div>
                <div class="contact-phone">${contact.phone}</div>
                ${contact.notes ? `<div class="contact-notes">${contact.notes}</div>` : ''}
            </div>
            <div class="contact-actions">
                <button class="btn-contact-action btn-edit-contact" onclick="editContact('${contact.id}')">
                    <i class="fas fa-edit"></i>
                </button>
                <button class="btn-contact-action btn-delete-contact" onclick="deleteContact('${contact.id}')">
                    <i class="fas fa-trash"></i>
                </button>
            </div>
        </div>
    `).join('');

    contactsList.innerHTML = contactsHTML;
}

// تحرير جهة اتصال
function editContact(id) {
    const contact = contacts.find(c => c.id === id);
    if (!contact) {
        showNotification('جهة الاتصال غير موجودة', 'error');
        return;
    }

    editingContactId = id;

    // ملء النموذج بالبيانات الحالية
    document.getElementById('contactName').value = contact.name;
    document.getElementById('contactPhone').value = contact.phone;
    document.getElementById('contactNotes').value = contact.notes || '';

    // إظهار النموذج
    const addContactForm = document.getElementById('addContactForm');
    if (addContactForm) {
        addContactForm.style.display = 'block';
    }

    // تغيير نص الزر
    const saveBtn = document.getElementById('saveContactBtn');
    if (saveBtn) {
        saveBtn.innerHTML = '<i class="fas fa-save"></i> تحديث';
    }
}

// إلغاء التحرير
function cancelContactEdit() {
    editingContactId = null;

    // مسح النموذج
    document.getElementById('contactName').value = '';
    document.getElementById('contactPhone').value = '';
    document.getElementById('contactNotes').value = '';

    // إخفاء النموذج
    const addContactForm = document.getElementById('addContactForm');
    if (addContactForm) {
        addContactForm.style.display = 'none';
    }

    // إعادة تعيين نص الزر
    const saveBtn = document.getElementById('saveContactBtn');
    if (saveBtn) {
        saveBtn.innerHTML = '<i class="fas fa-save"></i> حفظ';
    }
}

// تصدير جهات الاتصال
function exportContacts() {
    if (contacts.length === 0) {
        showNotification('لا توجد جهات اتصال للتصدير', 'error');
        return;
    }

    const dataStr = JSON.stringify(contacts, null, 2);
    const dataBlob = new Blob([dataStr], {type: 'application/json'});

    const link = document.createElement('a');
    link.href = URL.createObjectURL(dataBlob);
    link.download = `whatsapp_contacts_${new Date().toISOString().split('T')[0]}.json`;
    link.click();

    showNotification('تم تصدير جهات الاتصال بنجاح', 'success');
}

// استيراد جهات الاتصال
function importContacts(file) {
    const reader = new FileReader();
    reader.onload = function(e) {
        try {
            const importedContacts = JSON.parse(e.target.result);

            if (!Array.isArray(importedContacts)) {
                throw new Error('تنسيق الملف غير صحيح');
            }

            let addedCount = 0;
            let skippedCount = 0;

            importedContacts.forEach(contact => {
                if (contact.name && contact.phone) {
                    // التحقق من عدم وجود الرقم مسبقاً
                    const exists = contacts.find(c => c.phone === contact.phone);
                    if (!exists) {
                        const newContact = {
                            id: Date.now().toString() + Math.random().toString(36).substr(2, 9),
                            name: contact.name,
                            phone: contact.phone,
                            notes: contact.notes || '',
                            createdAt: new Date().toISOString()
                        };
                        contacts.push(newContact);
                        addedCount++;
                    } else {
                        skippedCount++;
                    }
                }
            });

            if (addedCount > 0) {
                saveContacts();
                renderContactsList();
                showNotification(`تم استيراد ${addedCount} جهة اتصال. تم تخطي ${skippedCount} جهة موجودة مسبقاً`, 'success');
            } else {
                showNotification('لم يتم استيراد أي جهة اتصال جديدة', 'info');
            }

        } catch (error) {
            console.error('خطأ في استيراد جهات الاتصال:', error);
            showNotification('فشل في استيراد جهات الاتصال. تأكد من تنسيق الملف', 'error');
        }
    };
    reader.readAsText(file);
}

// إعداد أحداث إدارة جهات الاتصال
function setupContactsEvents() {
    // زر إضافة جهة اتصال
    const addContactBtn = document.getElementById('addContactBtn');
    if (addContactBtn) {
        addContactBtn.addEventListener('click', () => {
            const addContactForm = document.getElementById('addContactForm');
            if (addContactForm) {
                addContactForm.style.display = addContactForm.style.display === 'none' ? 'block' : 'none';
            }
        });
    }

    // زر حفظ جهة اتصال
    const saveContactBtn = document.getElementById('saveContactBtn');
    if (saveContactBtn) {
        saveContactBtn.addEventListener('click', () => {
            const name = document.getElementById('contactName').value.trim();
            const phone = document.getElementById('contactPhone').value.trim();
            const notes = document.getElementById('contactNotes').value.trim();

            if (!name || !phone) {
                showNotification('يرجى إدخال الاسم ورقم الهاتف', 'error');
                return;
            }

            // التحقق من صحة رقم الهاتف
            if (!/^\d{10,15}$/.test(phone)) {
                showNotification('رقم الهاتف غير صحيح. يجب أن يحتوي على أرقام فقط (10-15 رقم)', 'error');
                return;
            }

            let success = false;
            if (editingContactId) {
                success = updateContact(editingContactId, name, phone, notes);
            } else {
                success = addContact(name, phone, notes);
            }

            if (success) {
                cancelContactEdit();
            }
        });
    }

    // زر إلغاء
    const cancelContactBtn = document.getElementById('cancelContactBtn');
    if (cancelContactBtn) {
        cancelContactBtn.addEventListener('click', cancelContactEdit);
    }

    // زر تصدير جهات الاتصال
    const exportContactsBtn = document.getElementById('exportContactsBtn');
    if (exportContactsBtn) {
        exportContactsBtn.addEventListener('click', exportContacts);
    }

    // زر استيراد جهات الاتصال
    const importContactsBtn = document.getElementById('importContactsBtn');
    const importContactsFile = document.getElementById('importContactsFile');

    if (importContactsBtn && importContactsFile) {
        importContactsBtn.addEventListener('click', () => {
            importContactsFile.click();
        });

        importContactsFile.addEventListener('change', (e) => {
            const file = e.target.files[0];
            if (file) {
                importContacts(file);
            }
        });
    }
}

// ===== اختيار جهات الاتصال =====

// إظهار نافذة اختيار جهة اتصال واحدة
function showContactSelector(targetInputId, multiple = false) {
    if (contacts.length === 0) {
        showNotification('لا توجد جهات اتصال محفوظة. أضف جهات اتصال أولاً', 'error');
        return;
    }

    const modal = document.createElement('div');
    modal.className = 'contact-selector-modal';
    modal.innerHTML = `
        <div class="contact-selector-content">
            <div class="contact-selector-header">
                <h3><i class="fas fa-address-book"></i> اختيار جهة اتصال</h3>
                <button class="btn-close-selector" onclick="closeContactSelector()">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <div class="contact-selector-list">
                ${contacts.map(contact => `
                    <div class="contact-selector-item" onclick="selectContact('${contact.id}', '${targetInputId}', ${multiple})">
                        ${multiple ? `<input type="checkbox" id="contact_${contact.id}" onchange="event.stopPropagation()">` : ''}
                        <div class="contact-info">
                            <div class="contact-name">${contact.name}</div>
                            <div class="contact-phone">${contact.phone}</div>
                            ${contact.notes ? `<div class="contact-notes">${contact.notes}</div>` : ''}
                        </div>
                    </div>
                `).join('')}
            </div>
            ${multiple ? `
                <div class="contact-selector-actions">
                    <button class="btn-confirm-selection" onclick="confirmMultipleSelection('${targetInputId}')">
                        <i class="fas fa-check"></i> تأكيد الاختيار
                    </button>
                    <button class="btn-cancel-selection" onclick="closeContactSelector()">
                        <i class="fas fa-times"></i> إلغاء
                    </button>
                </div>
            ` : ''}
        </div>
    `;

    document.body.appendChild(modal);

    // إضافة حدث الإغلاق عند النقر خارج النافذة
    modal.addEventListener('click', (e) => {
        if (e.target === modal) {
            closeContactSelector();
        }
    });
}

// إغلاق نافذة اختيار جهات الاتصال
function closeContactSelector() {
    const modal = document.querySelector('.contact-selector-modal');
    if (modal) {
        modal.remove();
    }
}

// اختيار جهة اتصال واحدة
function selectContact(contactId, targetInputId, multiple = false) {
    if (multiple) {
        const checkbox = document.getElementById(`contact_${contactId}`);
        if (checkbox) {
            checkbox.checked = !checkbox.checked;
        }
        return;
    }

    const contact = contacts.find(c => c.id === contactId);
    if (contact) {
        const targetInput = document.getElementById(targetInputId);
        if (targetInput) {
            targetInput.value = contact.phone;
        }
        closeContactSelector();
    }
}

// تأكيد اختيار متعدد
function confirmMultipleSelection(targetInputId) {
    const selectedContacts = [];
    contacts.forEach(contact => {
        const checkbox = document.getElementById(`contact_${contact.id}`);
        if (checkbox && checkbox.checked) {
            selectedContacts.push(contact.phone);
        }
    });

    if (selectedContacts.length === 0) {
        showNotification('يرجى اختيار جهة اتصال واحدة على الأقل', 'error');
        return;
    }

    const targetInput = document.getElementById(targetInputId);
    if (targetInput) {
        const currentValue = targetInput.value.trim();
        const newNumbers = selectedContacts.join('\n');

        if (currentValue) {
            targetInput.value = currentValue + '\n' + newNumbers;
        } else {
            targetInput.value = newNumbers;
        }
    }

    closeContactSelector();
    showNotification(`تم إضافة ${selectedContacts.length} رقم`, 'success');
}

// اختيار جميع جهات الاتصال
function selectAllContacts() {
    if (contacts.length === 0) {
        showNotification('لا توجد جهات اتصال محفوظة', 'error');
        return;
    }

    const allNumbers = contacts.map(contact => contact.phone).join('\n');
    const bulkNumbers = document.getElementById('bulkNumbers');

    if (bulkNumbers) {
        const currentValue = bulkNumbers.value.trim();
        if (currentValue) {
            bulkNumbers.value = currentValue + '\n' + allNumbers;
        } else {
            bulkNumbers.value = allNumbers;
        }
        showNotification(`تم إضافة ${contacts.length} رقم`, 'success');
    }
}

// إعداد أحداث اختيار جهات الاتصال
function setupContactSelectionEvents() {
    // زر اختيار جهة اتصال في الرسالة الفردية
    const selectContactSingle = document.getElementById('selectContactSingle');
    if (selectContactSingle) {
        selectContactSingle.addEventListener('click', () => {
            showContactSelector('phoneNumber', false);
        });
    }

    // زر اختيار جهة اتصال في إرسال الملفات
    const selectContactFile = document.getElementById('selectContactFile');
    if (selectContactFile) {
        selectContactFile.addEventListener('click', () => {
            showContactSelector('filePhoneNumber', false);
        });
    }

    // زر اختيار جهات اتصال في الرسائل الجماعية
    const selectContactsBulk = document.getElementById('selectContactsBulk');
    if (selectContactsBulk) {
        selectContactsBulk.addEventListener('click', () => {
            showContactSelector('bulkNumbers', true);
        });
    }

    // زر تحديد جميع جهات الاتصال
    const selectAllContactsBtn = document.getElementById('selectAllContacts');
    if (selectAllContactsBtn) {
        selectAllContactsBtn.addEventListener('click', selectAllContacts);
    }
}

// تهيئة الصفحة عند التحميل
document.addEventListener('DOMContentLoaded', function() {
    setupTabs();
    setupBulkMessageForm();
    setupAttachments();

    // تهيئة جهات الاتصال
    loadContacts();
    setupContactsEvents();
    setupContactSelectionEvents();
});
