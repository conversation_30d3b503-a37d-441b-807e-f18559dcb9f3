# 🔧 دليل استكشاف الأخطاء وإصلاحها

## مشكلة: ملف start.bat يختفي بسرعة

### 🔍 الأسباب المحتملة:

#### 1. **Node.js غير مثبت**
**الأعراض**: النافذة تظهر وتختفي فوراً
**الحل**:
```bash
# تحقق من وجود Node.js
node --version
```
إذا ظهر خطأ، حمل Node.js من: https://nodejs.org/

#### 2. **المتطلبات غير مثبتة**
**الأعراض**: رسالة خطأ سريعة
**الحل**:
```bash
npm install
```

#### 3. **المنفذ 3001 مستخدم**
**الأعراض**: خطأ "EADDRINUSE"
**الحل**:
```bash
# Windows
netstat -ano | findstr :3001
taskkill /PID [رقم_العملية] /F

# أو غير المنفذ في server.js
```

## 🛠️ خطوات الإصلاح:

### الخطوة 1: تشخيص المشكلة
1. اضغط مرتين على `diagnose.bat`
2. اقرأ النتائج بعناية
3. اتبع التوصيات

### الخطوة 2: التشغيل اليدوي
1. افتح Command Prompt
2. اذهب إلى مجلد المشروع:
   ```cmd
   cd "C:\Users\<USER>\Desktop\البرنامج\whatsapp-messenger-opensource"
   ```
3. شغل الأوامر واحداً تلو الآخر:
   ```cmd
   node --version
   npm --version
   npm install
   npm start
   ```

### الخطوة 3: التشغيل السريع
جرب `quick-start.bat` بدلاً من `start.bat`

## 🚨 الأخطاء الشائعة:

### خطأ: "node is not recognized"
**السبب**: Node.js غير مثبت أو غير موجود في PATH
**الحل**:
1. حمل Node.js من https://nodejs.org/
2. ثبته مع خيار "Add to PATH"
3. أعد تشغيل الكمبيوتر

### خطأ: "npm ERR! missing script: start"
**السبب**: ملف package.json مفقود أو تالف
**الحل**:
1. تأكد من وجود ملف `package.json`
2. إذا كان مفقوداً، أعد تحميل المشروع

### خطأ: "EADDRINUSE :::3001"
**السبب**: المنفذ 3001 مستخدم بالفعل
**الحل**:
```cmd
# إيجاد العملية
netstat -ano | findstr :3001

# إيقاف العملية
taskkill /PID [رقم_العملية] /F
```

### خطأ: "Cannot find module"
**السبب**: المتطلبات غير مثبتة
**الحل**:
```cmd
# حذف وإعادة تثبيت
rmdir /s node_modules
del package-lock.json
npm install
```

## 🔧 حلول متقدمة:

### تغيير المنفذ:
1. افتح `server.js`
2. غير السطر:
   ```javascript
   const PORT = process.env.PORT || 3001;
   ```
   إلى:
   ```javascript
   const PORT = process.env.PORT || 3002;
   ```

### تشغيل بصلاحيات إدارية:
1. اضغط بالزر الأيمن على `start.bat`
2. اختر "Run as administrator"

### تنظيف التثبيت:
```cmd
# حذف الملفات المؤقتة
rmdir /s node_modules
del package-lock.json
npm cache clean --force
npm install
```

## 📞 طلب المساعدة:

إذا لم تنجح الحلول أعلاه:

1. **شغل التشخيص**:
   ```cmd
   diagnose.bat
   ```

2. **جمع المعلومات**:
   - إصدار Windows
   - رسالة الخطأ الكاملة
   - نتائج التشخيص

3. **أنشئ Issue على GitHub** مع:
   - وصف المشكلة
   - خطوات إعادة الإنتاج
   - المعلومات المجمعة

## ✅ نصائح للوقاية:

1. **ثبت Node.js LTS** (النسخة المستقرة)
2. **لا تحذف مجلد node_modules** يدوياً
3. **أغلق التطبيق بـ Ctrl+C** وليس بإغلاق النافذة
4. **احتفظ بنسخة احتياطية** من المشروع
5. **تأكد من مساحة القرص** الكافية (500MB على الأقل)

---

**💡 نصيحة**: إذا كان التطبيق يعمل في Command Prompt ولكن لا يعمل مع start.bat، استخدم Command Prompt مباشرة.
